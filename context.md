# 🌟 ChatterHub Community Server Setup Guide

## 📋 Server Overview

**Server Name:** ChatterHub Community  
**Description:** A vibrant community server powered by ChatterBot's advanced ModMail system  
**Theme:** Modern tech community with comprehensive support infrastructure  

## 🎯 Server Structure

### 📂 Category Layout

```
🌟 WELCOME & INFO
├── 📢 announcements
├── 📋 rules-and-info
├── 🎉 welcome
└── 🤖 bot-commands

💬 GENERAL CHAT
├── 💭 general-chat
├── 🎮 gaming-lounge
├── 💻 tech-talk
└── 🎨 creative-corner

🛠️ DEVELOPMENT
├── 💡 project-showcase
├── 🐛 bug-reports
├── 💻 code-help
└── 🔧 dev-resources

🎓 LEARNING HUB
├── 📚 tutorials
├── ❓ questions-help
├── 🎯 challenges
└── 📖 resources

🎵 MEDIA & FUN
├── 🎵 music-sharing
├── 🖼️ art-gallery
├── 📺 media-discussion
└── 🎲 games-activities

🔧 SUPPORT SYSTEM
├── 🎫 create-ticket (ChatterBot Panel)
├── 📊 ticket-analytics
├── 🤖 ai-responses
└── 📝 support-logs

👑 STAFF AREA
├── 🛡️ staff-chat
├── 📋 staff-announcements
├── 🔍 moderation-logs
└── ⚙️ bot-management

🔒 MODMAIL TICKETS
├── (Auto-generated ticket channels)
└── (Managed by ChatterBot)
```

## 👥 Role Structure & Permissions

### 🎭 Community Roles

#### 🌟 **@Owner** 
- **Color:** #FF0000 (Red)
- **Permissions:** Administrator
- **Description:** Server owner with full control

#### 👑 **@Admin**
- **Color:** #FF6B35 (Orange-Red)
- **Permissions:** 
  - Manage Server
  - Manage Roles
  - Manage Channels
  - Manage Messages
  - Kick Members
  - Ban Members
  - View Audit Log
  - Manage Webhooks
- **ChatterBot Access:** Full admin commands

#### 🛡️ **@Moderator**
- **Color:** #F7931E (Orange)
- **Permissions:**
  - Manage Messages
  - Kick Members
  - Timeout Members
  - View Audit Log
  - Use Slash Commands
  - Manage Threads
- **ChatterBot Access:** Ticket management, blacklist, analytics

#### 🎫 **@ModMail Manager**
- **Color:** #4CAF50 (Green)
- **Permissions:**
  - View Channels (Support System category)
  - Send Messages
  - Embed Links
  - Attach Files
  - Read Message History
  - Use Slash Commands
  - Manage Messages (in ticket channels only)
- **ChatterBot Access:** All ticket commands, snippets, AI features
- **Special Access:** Full access to Support System category

#### 🤖 **@Support Agent**
- **Color:** #2196F3 (Blue)
- **Permissions:**
  - View Channels (Support System category)
  - Send Messages
  - Embed Links
  - Read Message History
  - Use Slash Commands
- **ChatterBot Access:** Basic ticket responses, snippets
- **Special Access:** Read-only in Support System category

### 🎨 Activity-Based Roles

#### 💻 **@Developer**
- **Color:** #9C27B0 (Purple)
- **Emoji:** 💻
- **Special Access:** Development category channels

#### 🎨 **@Creative**
- **Color:** #E91E63 (Pink)
- **Emoji:** 🎨
- **Special Access:** Media & Fun category channels

#### 🎓 **@Learner**
- **Color:** #00BCD4 (Cyan)
- **Emoji:** 🎓
- **Special Access:** Learning Hub category channels

#### 🎮 **@Gamer**
- **Color:** #8BC34A (Light Green)
- **Emoji:** 🎮
- **Special Access:** Gaming channels and voice rooms

### 🏆 Level-Based Roles

#### 🌱 **@Newcomer** (Level 1-5)
- **Color:** #CDDC39 (Lime)
- **Emoji:** 🌱

#### 🌿 **@Regular** (Level 6-15)
- **Color:** #4CAF50 (Green)
- **Emoji:** 🌿

#### 🌳 **@Veteran** (Level 16-30)
- **Color:** #2E7D32 (Dark Green)
- **Emoji:** 🌳

#### ⭐ **@Star Member** (Level 31+)
- **Color:** #FFD700 (Gold)
- **Emoji:** ⭐

## 🔐 Channel Permissions Setup

### 📢 **Announcements Channel**
```yaml
@everyone: 
  - View Channel: ✅
  - Send Messages: ❌
  - Read Message History: ✅

@Admin/@Moderator:
  - Send Messages: ✅
  - Manage Messages: ✅
  - Mention Everyone: ✅
```

### 💬 **General Chat Channels**
```yaml
@everyone:
  - View Channel: ✅
  - Send Messages: ✅
  - Read Message History: ✅
  - Use External Emojis: ✅
  - Add Reactions: ✅

@Newcomer:
  - Send Messages: ✅ (Rate limited: 1 message per 5 seconds)

@Regular and above:
  - Send Messages: ✅ (No rate limit)
```

### 🛠️ **Development Category**
```yaml
@everyone:
  - View Channel: ✅
  - Send Messages: ❌

@Developer/@Admin/@Moderator:
  - Send Messages: ✅
  - Embed Links: ✅
  - Attach Files: ✅

@Learner:
  - Send Messages: ✅ (Read-only in some channels)
```

### 🔧 **Support System Category**
```yaml
@everyone:
  - View Channel: ❌

@ModMail Manager:
  - View Channel: ✅
  - Send Messages: ✅
  - Manage Messages: ✅
  - Embed Links: ✅
  - Attach Files: ✅
  - Use Slash Commands: ✅

@Support Agent:
  - View Channel: ✅
  - Send Messages: ✅
  - Read Message History: ✅
  - Use Slash Commands: ✅

@Admin/@Moderator:
  - All permissions: ✅
```

### 🔒 **ModMail Tickets Category**
```yaml
@everyone:
  - View Channel: ❌

ChatterBot:
  - Manage Channels: ✅
  - View Channel: ✅
  - Send Messages: ✅
  - Manage Messages: ✅
  - Embed Links: ✅
  - Attach Files: ✅
  - Read Message History: ✅

@ModMail Manager/@Support Agent:
  - View Channel: ✅ (Only assigned tickets)
  - Send Messages: ✅
  - Read Message History: ✅
```

### 👑 **Staff Area**
```yaml
@everyone:
  - View Channel: ❌

@Admin/@Moderator/@ModMail Manager:
  - View Channel: ✅
  - Send Messages: ✅
  - Read Message History: ✅
  - Use Slash Commands: ✅
```

## 🤖 ChatterBot Configuration

### 📝 Environment Variables
```env
# Discord Configuration
DISCORD_TOKEN=your_bot_token_here
GUILD_ID=your_server_id_here

# OpenRouter AI Configuration
OPENROUTER_API_KEY=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

# Channel Configuration
MODMAIL_CATEGORY_ID=modmail_tickets_category_id
LOG_CHANNEL_ID=support_logs_channel_id

# Role Configuration (Multiple roles separated by commas)
STAFF_ROLE_ID=support_agent_role_id,modmail_manager_role_id
ADMIN_ROLE_ID=admin_role_id,owner_role_id
MODERATOR_ROLE_ID=moderator_role_id

# Advanced Features
ENABLE_AI_REPLIES=true
AUTO_CLOSE_INACTIVE_HOURS=72
MAX_TICKETS_PER_USER=3
```

### 🎫 Ticket Categories Setup
```javascript
// Configure in ChatterBot
const ticketCategories = {
  general: {
    name: "General Support",
    emoji: "📋",
    color: "#0099ff",
    pingRole: "support_agent_role_id",
    autoResponse: "Thank you for contacting general support. An agent will assist you shortly."
  },
  billing: {
    name: "Billing & Payments",
    emoji: "💳",
    color: "#00ff00",
    pingRole: "modmail_manager_role_id",
    autoResponse: "Thank you for contacting billing support. Please have your account information ready."
  },
  technical: {
    name: "Technical Issues",
    emoji: "🔧",
    color: "#ff9900",
    pingRole: "support_agent_role_id",
    autoResponse: "Thank you for reporting a technical issue. Please provide detailed information."
  },
  bug: {
    name: "Bug Reports",
    emoji: "🐛",
    color: "#ff0000",
    pingRole: "admin_role_id",
    autoResponse: "Thank you for the bug report. Please include steps to reproduce the issue."
  },
  feature: {
    name: "Feature Requests",
    emoji: "💡",
    color: "#9932cc",
    pingRole: "admin_role_id",
    autoResponse: "Thank you for your feature suggestion! Our team will review it."
  },
  other: {
    name: "Other Inquiries",
    emoji: "❓",
    color: "#808080",
    pingRole: "support_agent_role_id",
    autoResponse: "Thank you for contacting us. Please describe your inquiry."
  }
};
```

## 🚀 Setup Instructions

### 1️⃣ **Server Creation**
1. Create new Discord server named "ChatterHub Community"
2. Set server icon and banner
3. Configure server settings and verification level

### 2️⃣ **Category & Channel Creation**
1. Create categories in the order listed above
2. Create channels within each category
3. Set appropriate emojis for each channel
4. Configure channel topics and descriptions

### 3️⃣ **Role Setup**
1. Create roles in hierarchical order (Owner → Admin → Moderator → etc.)
2. Set role colors and permissions as specified
3. Assign role emojis and descriptions
4. Configure role hierarchy properly

### 4️⃣ **Permission Configuration**
1. Set category permissions first
2. Configure individual channel permissions
3. Test permissions with different roles
4. Ensure ModMail category is properly restricted

### 5️⃣ **ChatterBot Integration**
1. Invite ChatterBot with Administrator permissions
2. Configure environment variables
3. Set up ticket categories
4. Create ticket panel with `/ticket panel`
5. Test all features and commands

### 6️⃣ **Automation Setup**
1. Configure auto-responders with `/automation create`
2. Set up analytics monitoring
3. Create staff snippets for common responses
4. Test AI features and responses

## 📊 Recommended Server Settings

### 🔒 **Verification Level:** Medium
### 📱 **2FA Requirement:** Enabled for moderators
### 🛡️ **Explicit Content Filter:** Scan messages from members without roles
### 📢 **Default Notification:** Only @mentions
### 🎭 **Vanity URL:** chatter-hub (if available)

## 🎯 Success Metrics

- **Ticket Response Time:** < 2 hours
- **Resolution Rate:** > 95%
- **User Satisfaction:** > 4.5/5 stars
- **Staff Efficiency:** Measured via analytics
- **Community Growth:** Track member engagement

This setup creates a professional, scalable community server with enterprise-grade support infrastructure powered by ChatterBot's advanced features!

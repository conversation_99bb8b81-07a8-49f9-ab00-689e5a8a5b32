import asyncio
import discord
from datetime import datetime, timedelta
from typing import Dict, Any, Callable, Optional
from database import db
from config import config

class TaskScheduler:
    """Advanced task scheduler for ChatterBot"""
    
    def __init__(self, bot):
        self.bot = bot
        self.running = False
        self.task_handlers = {
            'close_ticket': self._handle_close_ticket,
            'reminder': self._handle_reminder,
            'auto_response': self._handle_auto_response
        }
    
    async def start(self):
        """Start the scheduler"""
        self.running = True
        asyncio.create_task(self._scheduler_loop())
    
    async def stop(self):
        """Stop the scheduler"""
        self.running = False
    
    async def _scheduler_loop(self):
        """Main scheduler loop"""
        while self.running:
            try:
                # Get due tasks
                due_tasks = await db.get_due_tasks()
                
                for task in due_tasks:
                    await self._execute_task(task)
                    await db.complete_task(task['id'])
                
                # Check for inactive tickets
                await self._check_inactive_tickets()
                
                # Sleep for 60 seconds before next check
                await asyncio.sleep(60)
                
            except Exception as e:
                print(f"Error in scheduler loop: {e}")
                await asyncio.sleep(60)
    
    async def _execute_task(self, task: Dict[str, Any]):
        """Execute a scheduled task"""
        task_type = task['task_type']
        
        if task_type in self.task_handlers:
            try:
                await self.task_handlers[task_type](task)
            except Exception as e:
                print(f"Error executing task {task_type}: {e}")
        else:
            print(f"Unknown task type: {task_type}")
    
    async def _handle_close_ticket(self, task: Dict[str, Any]):
        """Handle scheduled ticket closing"""
        channel_id = task['target_id']
        channel = self.bot.get_channel(channel_id)
        
        if not channel:
            return
        
        # Get ticket info
        ticket = await db.get_ticket(channel_id)
        if not ticket:
            return
        
        # Close the ticket
        await db.close_ticket(channel_id, self.bot.user.id, "Auto-closed due to inactivity")
        
        # Send closing message
        embed = discord.Embed(
            title="🔒 Ticket Auto-Closed",
            description="This ticket has been automatically closed due to inactivity.",
            color=discord.Color.orange(),
            timestamp=datetime.now()
        )
        
        try:
            await channel.send(embed=embed)
            
            # Wait a bit then delete the channel
            await asyncio.sleep(10)
            await channel.delete(reason="Ticket auto-closed")
            
        except discord.NotFound:
            pass  # Channel already deleted
        except discord.Forbidden:
            print(f"Missing permissions to close ticket channel {channel_id}")
    
    async def _handle_reminder(self, task: Dict[str, Any]):
        """Handle reminder tasks"""
        import json
        
        data = json.loads(task['data']) if task['data'] else {}
        channel_id = data.get('channel_id')
        message = data.get('message', 'Reminder!')
        
        if channel_id:
            channel = self.bot.get_channel(channel_id)
            if channel:
                embed = discord.Embed(
                    title="⏰ Reminder",
                    description=message,
                    color=discord.Color.blue(),
                    timestamp=datetime.now()
                )
                try:
                    await channel.send(embed=embed)
                except discord.Forbidden:
                    pass
    
    async def _handle_auto_response(self, task: Dict[str, Any]):
        """Handle auto-response tasks"""
        import json
        
        data = json.loads(task['data']) if task['data'] else {}
        channel_id = data.get('channel_id')
        response = data.get('response', '')
        
        if channel_id and response:
            channel = self.bot.get_channel(channel_id)
            if channel:
                try:
                    await channel.send(response)
                except discord.Forbidden:
                    pass
    
    async def _check_inactive_tickets(self):
        """Check for inactive tickets and schedule auto-close"""
        auto_close_hours = config.get('modmail.auto_close_hours', 72)
        if auto_close_hours <= 0:
            return
        
        # This would require a more complex query to find inactive tickets
        # For now, we'll implement a basic version
        pass
    
    async def schedule_ticket_close(self, channel_id: int, delay: timedelta, 
                                  reason: str = "Scheduled close"):
        """Schedule a ticket to be closed"""
        scheduled_for = datetime.now() + delay
        
        await db.schedule_task(
            task_type='close_ticket',
            target_id=channel_id,
            scheduled_for=scheduled_for,
            data={'reason': reason}
        )
    
    async def schedule_reminder(self, channel_id: int, delay: timedelta, 
                              message: str):
        """Schedule a reminder"""
        scheduled_for = datetime.now() + delay
        
        await db.schedule_task(
            task_type='reminder',
            target_id=0,  # Not used for reminders
            scheduled_for=scheduled_for,
            data={'channel_id': channel_id, 'message': message}
        )
    
    async def cancel_scheduled_tasks(self, target_id: int, task_type: str = None):
        """Cancel scheduled tasks for a target"""
        # This would require additional database methods
        # For now, we'll mark them as completed
        pass

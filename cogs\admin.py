import discord
from discord.ext import commands
import asyncio
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional

from database import db
from config import config
from utils.permissions import is_admin, is_moderator, PermissionManager
from utils.helpers import <PERSON><PERSON><PERSON><PERSON><PERSON>, TimeParser, PaginationView
from utils.scheduler import TaskScheduler

class Admin(commands.Cog):
    """Advanced admin commands for ChatterBot"""
    
    def __init__(self, bot):
        self.bot = bot
        self.scheduler = TaskScheduler(bot)
    
    async def cog_load(self):
        """Start scheduler when cog loads"""
        await self.scheduler.start()
    
    async def cog_unload(self):
        """Stop scheduler when cog unloads"""
        await self.scheduler.stop()
    
    @commands.group(name='blacklist', aliases=['bl'], invoke_without_command=True)
    @is_moderator()
    async def blacklist(self, ctx):
        """Blacklist management commands"""
        embed = EmbedBuilder.info(
            "Blacklist Commands",
            "Manage user blacklist for ModMail"
        )
        embed.add_field(
            name="Commands",
            value="`!blacklist add <user> [reason]` - Blacklist a user\n"
                  "`!blacklist remove <user>` - Remove user from blacklist\n"
                  "`!blacklist list` - List blacklisted users\n"
                  "`!blacklist check <user>` - Check if user is blacklisted",
            inline=False
        )
        await ctx.send(embed=embed)
    
    @blacklist.command(name='add')
    @is_moderator()
    async def blacklist_add(self, ctx, user: discord.User, *, reason: str = "No reason provided"):
        """Blacklist a user from creating tickets"""
        success = await db.blacklist_user(user.id, reason, ctx.author.id)
        
        if success:
            embed = EmbedBuilder.success(
                "User Blacklisted",
                f"{user.mention} has been blacklisted from creating tickets."
            )
            embed.add_field(name="Reason", value=reason, inline=False)
            
            # Close any open tickets for this user
            open_tickets = await db.get_user_tickets(user.id, "open")
            for ticket in open_tickets:
                channel = self.bot.get_channel(ticket['channel_id'])
                if channel:
                    await db.close_ticket(
                        channel.id, 
                        ctx.author.id, 
                        f"User blacklisted: {reason}"
                    )
                    
                    embed_close = EmbedBuilder.warning(
                        "Ticket Closed",
                        "This ticket has been closed because the user was blacklisted."
                    )
                    await channel.send(embed=embed_close)
                    
                    # Schedule channel deletion
                    await self.scheduler.schedule_ticket_close(
                        channel.id, 
                        timedelta(seconds=30),
                        "User blacklisted"
                    )
        else:
            embed = EmbedBuilder.error(
                "Blacklist Failed",
                "Failed to blacklist user. They may already be blacklisted."
            )
        
        await ctx.send(embed=embed)
    
    @blacklist.command(name='remove', aliases=['unblacklist'])
    @is_moderator()
    async def blacklist_remove(self, ctx, user: discord.User):
        """Remove a user from the blacklist"""
        success = await db.unblacklist_user(user.id)
        
        if success:
            embed = EmbedBuilder.success(
                "User Unblacklisted",
                f"{user.mention} has been removed from the blacklist."
            )
        else:
            embed = EmbedBuilder.error(
                "User Not Blacklisted",
                f"{user.mention} is not currently blacklisted."
            )
        
        await ctx.send(embed=embed)
    
    @blacklist.command(name='check')
    @is_moderator()
    async def blacklist_check(self, ctx, user: discord.User):
        """Check if a user is blacklisted"""
        is_blacklisted = await db.is_blacklisted(user.id)
        
        if is_blacklisted:
            embed = EmbedBuilder.warning(
                "User Blacklisted",
                f"{user.mention} is currently blacklisted."
            )
        else:
            embed = EmbedBuilder.success(
                "User Not Blacklisted",
                f"{user.mention} is not blacklisted."
            )
        
        await ctx.send(embed=embed)
    
    @commands.command(name='closeall')
    @is_admin()
    async def close_all_tickets(self, ctx, *, reason: str = "Mass closure by admin"):
        """Close all open tickets"""
        # Get all open tickets
        # This would require a database method to get all open tickets
        
        embed = EmbedBuilder.warning(
            "Confirm Mass Closure",
            "Are you sure you want to close ALL open tickets?\n"
            f"**Reason:** {reason}"
        )
        
        view = ConfirmationView(ctx.author.id)
        message = await ctx.send(embed=embed, view=view)
        await view.wait()
        
        if view.confirmed:
            # Close all tickets (implementation needed)
            embed = EmbedBuilder.success(
                "All Tickets Closed",
                f"All open tickets have been closed.\n**Reason:** {reason}"
            )
        else:
            embed = EmbedBuilder.info(
                "Operation Cancelled",
                "Mass ticket closure was cancelled."
            )
        
        await message.edit(embed=embed, view=None)
    
    @commands.command(name='aclose')
    @is_moderator()
    async def anonymous_close(self, ctx, *, reason: str = "No reason provided"):
        """Close ticket anonymously"""
        ticket = await db.get_ticket(ctx.channel.id)
        if not ticket:
            embed = EmbedBuilder.error("Not a Ticket", "This command can only be used in ticket channels.")
            await ctx.send(embed=embed)
            return
        
        # Close the ticket anonymously
        success = await db.close_ticket(ctx.channel.id, ctx.author.id, reason, is_anonymous=True)
        
        if success:
            # Notify user
            user = self.bot.get_user(ticket['user_id'])
            if user:
                embed = EmbedBuilder.info(
                    "Ticket Closed", 
                    config.get('messages.anonymous_close_message')
                )
                if reason != "No reason provided":
                    embed.add_field(name="Reason", value=reason, inline=False)
                await user.send(embed=embed)
            
            # Send confirmation
            embed = EmbedBuilder.success("Ticket Closed Anonymously", f"Reason: {reason}")
            await ctx.send(embed=embed)
            
            # Delete channel after delay
            await asyncio.sleep(10)
            try:
                await ctx.channel.delete(reason="Ticket closed anonymously")
            except discord.NotFound:
                pass
    
    @commands.command(name='areply')
    @is_moderator()
    async def anonymous_reply(self, ctx, *, message: str):
        """Send an anonymous reply to the ticket"""
        ticket = await db.get_ticket(ctx.channel.id)
        if not ticket:
            embed = EmbedBuilder.error("Not a Ticket", "This command can only be used in ticket channels.")
            await ctx.send(embed=embed)
            return
        
        # Get user
        user = self.bot.get_user(ticket['user_id'])
        if not user:
            embed = EmbedBuilder.error("User Not Found", "Could not find the ticket user.")
            await ctx.send(embed=embed)
            return
        
        # Send anonymous message to user
        embed = discord.Embed(
            description=message,
            color=discord.Color.green(),
            timestamp=datetime.now()
        )
        embed.set_author(name="Support Team", icon_url=ctx.guild.icon.url if ctx.guild.icon else None)
        
        try:
            await user.send(embed=embed)
            
            # Log the anonymous message
            await db.log_message(
                ticket['id'], 0, ctx.author.id, message, 
                is_staff=True, is_anonymous=True
            )
            
            # Confirm in channel
            embed = EmbedBuilder.success("Anonymous Reply Sent", "Your message has been sent anonymously.")
            await ctx.send(embed=embed, delete_after=5)
            
            # Delete the command message
            try:
                await ctx.message.delete()
            except discord.NotFound:
                pass
                
        except discord.Forbidden:
            embed = EmbedBuilder.error("Cannot Send DM", "Unable to send DM to user.")
            await ctx.send(embed=embed)
    
    @commands.command(name='schedule')
    @is_moderator()
    async def schedule_close(self, ctx, time: str, *, reason: str = "Scheduled closure"):
        """Schedule a ticket to close after specified time"""
        ticket = await db.get_ticket(ctx.channel.id)
        if not ticket:
            embed = EmbedBuilder.error("Not a Ticket", "This command can only be used in ticket channels.")
            await ctx.send(embed=embed)
            return
        
        # Parse time
        delay = TimeParser.parse_time(time)
        if not delay:
            embed = EmbedBuilder.error(
                "Invalid Time Format",
                "Please use format like: `1h`, `30m`, `2d`, etc."
            )
            await ctx.send(embed=embed)
            return
        
        # Schedule the closure
        await self.scheduler.schedule_ticket_close(ctx.channel.id, delay, reason)
        
        # Confirm scheduling
        time_str = TimeParser.format_timedelta(delay)
        embed = EmbedBuilder.success(
            "Ticket Scheduled for Closure",
            f"This ticket will be closed in {time_str}.\n**Reason:** {reason}"
        )
        await ctx.send(embed=embed)
    
    @commands.command(name='stats')
    @is_admin()
    async def bot_stats(self, ctx):
        """Show bot statistics"""
        # This would require additional database queries
        embed = EmbedBuilder.info("ChatterBot Statistics", "")
        embed.add_field(name="Total Tickets", value="N/A", inline=True)
        embed.add_field(name="Open Tickets", value="N/A", inline=True)
        embed.add_field(name="Closed Today", value="N/A", inline=True)
        embed.add_field(name="Total Users", value="N/A", inline=True)
        embed.add_field(name="Blacklisted Users", value="N/A", inline=True)
        embed.add_field(name="Total Snippets", value="N/A", inline=True)
        
        await ctx.send(embed=embed)

class ConfirmationView(discord.ui.View):
    """Confirmation view for dangerous operations"""
    
    def __init__(self, user_id: int):
        super().__init__(timeout=30)
        self.user_id = user_id
        self.confirmed = None
    
    @discord.ui.button(label='Confirm', style=discord.ButtonStyle.danger)
    async def confirm(self, interaction: discord.Interaction, button: discord.ui.Button):
        if interaction.user.id != self.user_id:
            await interaction.response.send_message("You can't use this button.", ephemeral=True)
            return
        
        self.confirmed = True
        self.stop()
        await interaction.response.defer()
    
    @discord.ui.button(label='Cancel', style=discord.ButtonStyle.secondary)
    async def cancel(self, interaction: discord.Interaction, button: discord.ui.Button):
        if interaction.user.id != self.user_id:
            await interaction.response.send_message("You can't use this button.", ephemeral=True)
            return
        
        self.confirmed = False
        self.stop()
        await interaction.response.defer()

async def setup(bot):
    await bot.add_cog(Admin(bot))

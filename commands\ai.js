const { <PERSON>lash<PERSON>ommandBuilder, EmbedBuilder } = require('discord.js');
const OpenAI = require('openai');
const database = require('../database/database');
const config = require('../config/config');
const { Embed<PERSON>elper, ComponentHelper, safeReply, safeSend } = require('../utils/helpers');
const { requireTicketManager } = require('../utils/permissions');

class AIFeatures {
    constructor(client) {
        this.client = client;
        this.openai = null;
        this.setupOpenRouter();
    }

    setupOpenRouter() {
        const apiKey = config.openrouterApiKey;

        if (apiKey && config.get('ai_features.enabled')) {
            try {
                this.openai = new OpenAI({
                    apiKey: apiKey,
                    baseURL: 'https://openrouter.ai/api/v1'
                });
                console.log('✅ OpenRouter AI initialized');
            } catch (error) {
                console.error('❌ Failed to initialize OpenRouter:', error);
            }
        } else {
            console.log('⚠️ OpenRouter AI not configured');
        }
    }

    async generateAIReply(interaction) {
        if (!this.openai) {
            const embed = EmbedHelper.error('AI Not Available', 'AI features are not enabled or configured.');
            await safeReply(interaction, { embeds: [embed], ephemeral: true });
            return;
        }

        // Check if this is a ticket channel
        const ticket = await database.getTicket(interaction.channel.id);
        if (!ticket) {
            const embed = EmbedHelper.error('Not a Ticket', 'This command can only be used in ticket channels.');
            await safeReply(interaction, { embeds: [embed], ephemeral: true });
            return;
        }

        await interaction.deferReply({ ephemeral: true });

        try {
            // Get recent messages for context
            const messages = await this.getConversationHistory(interaction.channel, ticket.user_id);
            
            // Get additional prompt if provided
            const prompt = interaction.options.getString('prompt') || '';
            
            // Generate AI response using OpenRouter
            const response = await this.generateResponse(messages, prompt);
            
            if (response) {
                // Create embed for AI response
                const embed = EmbedHelper.info('🤖 AI-Generated Response', response)
                    .setColor(0x9932cc)
                    .setFooter({ text: 'Review and edit before sending to user' });

                // Create response ID for button interactions
                const responseId = Date.now().toString();
                
                // Store response temporarily (in production, use Redis or similar)
                this.client.aiResponses = this.client.aiResponses || new Map();
                this.client.aiResponses.set(responseId, {
                    content: response,
                    userId: ticket.user_id,
                    channelId: interaction.channel.id,
                    createdAt: Date.now()
                });

                // Add action buttons
                const buttons = ComponentHelper.createAIResponseButtons(responseId);
                
                await interaction.editReply({ embeds: [embed], components: [buttons] });
            } else {
                const embed = EmbedHelper.error('AI Error', 'Failed to generate AI response. Please try again.');
                await interaction.editReply({ embeds: [embed] });
            }
        } catch (error) {
            console.error('Error generating AI response:', error);
            const embed = EmbedHelper.error('AI Error', `An error occurred: ${error.message}`);
            await interaction.editReply({ embeds: [embed] });
        }
    }

    async getConversationHistory(channel, userId) {
        const messages = [];
        
        try {
            // Fetch recent messages from the channel
            const fetchedMessages = await channel.messages.fetch({ limit: 20 });
            
            for (const message of fetchedMessages.values()) {
                if (message.author.bot && message.embeds.length > 0) {
                    // Extract content from embeds (forwarded messages)
                    const embed = message.embeds[0];
                    if (embed.description) {
                        const role = embed.color === 0x0099ff ? 'user' : 'assistant';
                        messages.unshift({
                            role: role,
                            content: embed.description
                        });
                    }
                } else if (!message.author.bot && message.content) {
                    // Direct staff messages
                    messages.unshift({
                        role: 'assistant',
                        content: message.content
                    });
                }
            }
        } catch (error) {
            console.error('Error fetching conversation history:', error);
        }

        return messages.slice(-10); // Keep last 10 messages for context
    }

    async generateResponse(conversationHistory, additionalPrompt = '') {
        try {
            // Prepare the conversation for OpenRouter
            let systemPrompt = `You are a helpful customer support assistant.
                Provide professional, empathetic, and helpful responses to customer inquiries.
                Keep responses concise but thorough.
                If you need more information, ask clarifying questions.`;

            if (additionalPrompt) {
                systemPrompt += ` Additional context: ${additionalPrompt}`;
            }

            // Use OpenRouter AI to generate response
            const response = await this.openai.chat.completions.create({
                messages: [
                    { role: 'system', content: systemPrompt },
                    ...conversationHistory
                ],
                model: config.get('ai_features.model', 'meta-llama/llama-3.2-3b-instruct:free'),
                max_tokens: config.get('ai_features.max_tokens', 500),
                temperature: config.get('ai_features.temperature', 0.7)
            });

            return response.choices[0].message.content.trim();
        } catch (error) {
            console.error('OpenRouter AI error:', error);
            return null;
        }
    }

    async summarizeTicket(interaction) {
        if (!this.openai) {
            const embed = EmbedHelper.error('AI Not Available', 'AI features are not enabled or configured.');
            await safeReply(interaction, { embeds: [embed], ephemeral: true });
            return;
        }

        const ticket = await database.getTicket(interaction.channel.id);
        if (!ticket) {
            const embed = EmbedHelper.error('Not a Ticket', 'This command can only be used in ticket channels.');
            await safeReply(interaction, { embeds: [embed], ephemeral: true });
            return;
        }

        await interaction.deferReply({ ephemeral: true });

        try {
            // Get all messages in the ticket
            const messages = [];
            const fetchedMessages = await interaction.channel.messages.fetch({ limit: 100 });
            
            for (const message of fetchedMessages.reverse().values()) {
                let content = message.content;
                
                // Extract content from embeds if it's a forwarded message
                if (message.embeds.length > 0 && message.author.bot) {
                    const embed = message.embeds[0];
                    if (embed.description) {
                        content = embed.description;
                    }
                }

                if (content) {
                    const author = message.author.id === ticket.user_id ? 'User' : 'Staff';
                    messages.push(`${author}: ${content}`);
                }
            }

            if (messages.length === 0) {
                const embed = EmbedHelper.warning('No Messages', 'No messages found to summarize.');
                await interaction.editReply({ embeds: [embed] });
                return;
            }

            // Generate summary using OpenRouter
            const conversationText = messages.join('\n');
            const summary = await this.openai.chat.completions.create({
                messages: [
                    {
                        role: 'system',
                        content: 'Summarize the following customer support conversation. Include the main issue, key points discussed, and current status. Keep it concise but comprehensive.'
                    },
                    {
                        role: 'user',
                        content: conversationText
                    }
                ],
                model: config.get('ai_features.model', 'meta-llama/llama-3.2-3b-instruct:free'),
                max_tokens: 300
            });

            if (summary) {
                const embed = EmbedHelper.info('🤖 Ticket Summary', summary.choices[0].message.content)
                    .setFooter({ text: 'AI-generated summary' });
                await interaction.editReply({ embeds: [embed] });
            } else {
                const embed = EmbedHelper.error('Summary Failed', 'Failed to generate ticket summary.');
                await interaction.editReply({ embeds: [embed] });
            }
        } catch (error) {
            console.error('Error generating summary:', error);
            const embed = EmbedHelper.error('Summary Failed', `An error occurred: ${error.message}`);
            await interaction.editReply({ embeds: [embed] });
        }
    }

    async translateMessage(interaction) {
        if (!this.openai) {
            const embed = EmbedHelper.error('AI Not Available', 'AI features are not enabled or configured.');
            await safeReply(interaction, { embeds: [embed], ephemeral: true });
            return;
        }

        const targetLanguage = interaction.options.getString('language');
        let text = interaction.options.getString('text');

        // If no text provided, try to get from replied message
        if (!text && interaction.targetMessage) {
            text = interaction.targetMessage.content;
            
            // Extract from embed if it's a forwarded message
            if (interaction.targetMessage.embeds.length > 0 && interaction.targetMessage.author.bot) {
                const embed = interaction.targetMessage.embeds[0];
                if (embed.description) {
                    text = embed.description;
                }
            }
        }

        if (!text) {
            const embed = EmbedHelper.error('No Text', 'Please provide text to translate.');
            await safeReply(interaction, { embeds: [embed], ephemeral: true });
            return;
        }

        await interaction.deferReply({ ephemeral: true });

        try {
            const translation = await this.openai.chat.completions.create({
                messages: [
                    {
                        role: 'system',
                        content: `Translate the following text to ${targetLanguage}. Only provide the translation, no additional text.`
                    },
                    {
                        role: 'user',
                        content: text
                    }
                ],
                model: config.get('ai_features.model', 'meta-llama/llama-3.2-3b-instruct:free'),
                max_tokens: 200
            });

            if (translation) {
                const embed = EmbedHelper.info(`🌐 Translation to ${targetLanguage}`, '')
                    .addFields(
                        { name: 'Original', value: text.substring(0, 1000), inline: false },
                        { name: 'Translation', value: translation.choices[0].message.content.substring(0, 1000), inline: false }
                    );
                await interaction.editReply({ embeds: [embed] });
            } else {
                const embed = EmbedHelper.error('Translation Failed', 'Failed to translate the text.');
                await interaction.editReply({ embeds: [embed] });
            }
        } catch (error) {
            console.error('Error translating text:', error);
            const embed = EmbedHelper.error('Translation Failed', `An error occurred: ${error.message}`);
            await interaction.editReply({ embeds: [embed] });
        }
    }

    async handleAIResponseButton(interaction) {
        const [action, responseId] = interaction.customId.split('_').slice(1);
        
        if (!this.client.aiResponses || !this.client.aiResponses.has(responseId)) {
            await safeReply(interaction, { content: '❌ AI response not found or expired.', ephemeral: true });
            return;
        }

        const responseData = this.client.aiResponses.get(responseId);

        switch (action) {
            case 'send':
                await this.sendAIResponseToUser(interaction, responseData);
                break;
            case 'edit':
                await this.editAIResponse(interaction, responseData);
                break;
            case 'discard':
                await this.discardAIResponse(interaction, responseId);
                break;
        }
    }

    async sendAIResponseToUser(interaction, responseData) {
        const user = this.client.users.cache.get(responseData.userId);
        if (!user) {
            await safeReply(interaction, { content: '❌ User not found.', ephemeral: true });
            return;
        }

        // Send response to user
        const embed = new EmbedBuilder()
            .setDescription(responseData.content)
            .setColor(0x00ff00)
            .setAuthor({ 
                name: 'Support Team', 
                iconURL: interaction.guild.iconURL() || undefined 
            })
            .setTimestamp();

        try {
            await safeSend(user, { embeds: [embed] });
            await safeReply(interaction, { content: '✅ Response sent to user!', ephemeral: true });
            
            // Clean up stored response
            this.client.aiResponses.delete(interaction.customId.split('_')[2]);
            
            // Disable buttons
            await interaction.message.edit({ components: [] });
        } catch (error) {
            await safeReply(interaction, { content: '❌ Cannot send DM to user.', ephemeral: true });
        }
    }

    async editAIResponse(interaction, responseData) {
        // This would typically open a modal for editing
        // For now, we'll just acknowledge the action
        await safeReply(interaction, { 
            content: '✏️ Edit functionality would open a modal here. For now, you can copy and modify the response manually.', 
            ephemeral: true 
        });
    }

    async discardAIResponse(interaction, responseId) {
        this.client.aiResponses.delete(responseId);
        await safeReply(interaction, { content: '🗑️ AI response discarded.', ephemeral: true });
        await interaction.message.edit({ components: [] });
    }
}

// Slash command definitions
const commands = [
    new SlashCommandBuilder()
        .setName('aireply')
        .setDescription('Generate an AI-powered reply for the current ticket')
        .addStringOption(option =>
            option.setName('prompt')
                .setDescription('Additional context or instructions for the AI')
                .setRequired(false)
        ),
    
    new SlashCommandBuilder()
        .setName('summarize')
        .setDescription('Generate an AI summary of the current ticket'),
    
    new SlashCommandBuilder()
        .setName('translate')
        .setDescription('Translate text using AI')
        .addStringOption(option =>
            option.setName('language')
                .setDescription('Target language for translation')
                .setRequired(true)
        )
        .addStringOption(option =>
            option.setName('text')
                .setDescription('Text to translate (leave empty to translate a replied message)')
                .setRequired(false)
        )
];

module.exports = {
    AIFeatures,
    commands
};

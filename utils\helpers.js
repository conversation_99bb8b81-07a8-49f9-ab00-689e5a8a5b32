const { Em<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ButtonStyle } = require('discord.js');
const moment = require('moment');

class EmbedHelper {
    static success(title, description = null) {
        const embed = new EmbedBuilder()
            .setTitle(`✅ ${title}`)
            .setColor(0x00ff00)
            .setTimestamp();
        
        if (description) embed.setDescription(description);
        return embed;
    }

    static error(title, description = null) {
        const embed = new EmbedBuilder()
            .setTitle(`❌ ${title}`)
            .setColor(0xff0000)
            .setTimestamp();
        
        if (description) embed.setDescription(description);
        return embed;
    }

    static warning(title, description = null) {
        const embed = new EmbedBuilder()
            .setTitle(`⚠️ ${title}`)
            .setColor(0xffa500)
            .setTimestamp();
        
        if (description) embed.setDescription(description);
        return embed;
    }

    static info(title, description = null) {
        const embed = new EmbedBuilder()
            .setTitle(`ℹ️ ${title}`)
            .setColor(0x0099ff)
            .setTimestamp();
        
        if (description) embed.setDescription(description);
        return embed;
    }

    static ticketCreated(user, ticketId, category = 'General') {
        return new EmbedBuilder()
            .setTitle('🎫 New Support Ticket')
            .setDescription(`Ticket created by ${user}`)
            .setColor(0x0099ff)
            .addFields(
                { name: 'User', value: `${user} (${user.id})`, inline: true },
                { name: 'Category', value: category, inline: true },
                { name: 'Ticket ID', value: `#${ticketId}`, inline: true }
            )
            .setThumbnail(user.displayAvatarURL())
            .setTimestamp();
    }

    static ticketClosed(user, staff, reason = null, isAnonymous = false) {
        const embed = new EmbedBuilder()
            .setTitle('🔒 Ticket Closed')
            .setColor(0xff0000)
            .setTimestamp();

        if (isAnonymous) {
            embed.addFields({ name: 'Closed by', value: 'Staff Member', inline: true });
        } else {
            embed.addFields({ name: 'Closed by', value: `${staff} (${staff.id})`, inline: true });
        }

        embed.addFields({ name: 'User', value: `${user} (${user.id})`, inline: true });

        if (reason) {
            embed.addFields({ name: 'Reason', value: reason, inline: false });
        }

        return embed;
    }
}

class MessageFormatter {
    static formatMessage(template, variables) {
        let formatted = template;
        for (const [key, value] of Object.entries(variables)) {
            formatted = formatted.replace(new RegExp(`{${key}}`, 'g'), value);
        }
        return formatted;
    }

    static truncateText(text, maxLength = 2000) {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength - 3) + '...';
    }

    static cleanContent(content) {
        // Remove mentions and clean up formatting
        return content
            .replace(/<@!?(\d+)>/g, '@$1')
            .replace(/<#(\d+)>/g, '#$1')
            .replace(/<@&(\d+)>/g, '@role$1');
    }
}

class TimeParser {
    static parseTime(timeStr) {
        if (!timeStr) return null;

        const regex = /(\d+)\s*(s|sec|second|seconds|m|min|minute|minutes|h|hour|hours|d|day|days|w|week|weeks)/gi;
        const matches = [...timeStr.matchAll(regex)];
        
        let totalMs = 0;

        for (const match of matches) {
            const amount = parseInt(match[1]);
            const unit = match[2].toLowerCase();

            switch (unit) {
                case 's':
                case 'sec':
                case 'second':
                case 'seconds':
                    totalMs += amount * 1000;
                    break;
                case 'm':
                case 'min':
                case 'minute':
                case 'minutes':
                    totalMs += amount * 60 * 1000;
                    break;
                case 'h':
                case 'hour':
                case 'hours':
                    totalMs += amount * 60 * 60 * 1000;
                    break;
                case 'd':
                case 'day':
                case 'days':
                    totalMs += amount * 24 * 60 * 60 * 1000;
                    break;
                case 'w':
                case 'week':
                case 'weeks':
                    totalMs += amount * 7 * 24 * 60 * 60 * 1000;
                    break;
            }
        }

        return totalMs > 0 ? new Date(Date.now() + totalMs) : null;
    }

    static formatDuration(ms) {
        const duration = moment.duration(ms);
        const parts = [];

        if (duration.days()) parts.push(`${duration.days()} day${duration.days() !== 1 ? 's' : ''}`);
        if (duration.hours()) parts.push(`${duration.hours()} hour${duration.hours() !== 1 ? 's' : ''}`);
        if (duration.minutes()) parts.push(`${duration.minutes()} minute${duration.minutes() !== 1 ? 's' : ''}`);
        if (duration.seconds() && !duration.days() && !duration.hours()) {
            parts.push(`${duration.seconds()} second${duration.seconds() !== 1 ? 's' : ''}`);
        }

        return parts.length ? parts.join(', ') : '0 seconds';
    }
}

class ComponentHelper {
    static createTicketButtons(ticketId) {
        return new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`close_ticket_${ticketId}`)
                    .setLabel('Close Ticket')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('🔒'),
                new ButtonBuilder()
                    .setCustomId(`assign_ticket_${ticketId}`)
                    .setLabel('Assign to Me')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('👤')
            );
    }

    static createConfirmationButtons(customId) {
        return new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`confirm_${customId}`)
                    .setLabel('Confirm')
                    .setStyle(ButtonStyle.Danger),
                new ButtonBuilder()
                    .setCustomId(`cancel_${customId}`)
                    .setLabel('Cancel')
                    .setStyle(ButtonStyle.Secondary)
            );
    }

    static createPaginationButtons(currentPage, totalPages) {
        const row = new ActionRowBuilder();

        if (currentPage > 0) {
            row.addComponents(
                new ButtonBuilder()
                    .setCustomId('previous_page')
                    .setLabel('◀️')
                    .setStyle(ButtonStyle.Secondary)
            );
        }

        if (currentPage < totalPages - 1) {
            row.addComponents(
                new ButtonBuilder()
                    .setCustomId('next_page')
                    .setLabel('▶️')
                    .setStyle(ButtonStyle.Secondary)
            );
        }

        row.addComponents(
            new ButtonBuilder()
                .setCustomId('delete_message')
                .setLabel('🗑️')
                .setStyle(ButtonStyle.Danger)
        );

        return row;
    }

    static createAIResponseButtons(responseId) {
        return new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`send_ai_response_${responseId}`)
                    .setLabel('Send to User')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('📤'),
                new ButtonBuilder()
                    .setCustomId(`edit_ai_response_${responseId}`)
                    .setLabel('Edit Response')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('✏️'),
                new ButtonBuilder()
                    .setCustomId(`discard_ai_response_${responseId}`)
                    .setLabel('Discard')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('🗑️')
            );
    }
}

async function safeSend(destination, options) {
    try {
        return await destination.send(options);
    } catch (error) {
        console.error('Error sending message:', error);
        return null;
    }
}

async function safeEdit(message, options) {
    try {
        return await message.edit(options);
    } catch (error) {
        console.error('Error editing message:', error);
        return false;
    }
}

async function safeReply(interaction, options) {
    try {
        if (interaction.replied || interaction.deferred) {
            return await interaction.editReply(options);
        } else {
            return await interaction.reply(options);
        }
    } catch (error) {
        console.error('Error replying to interaction:', error);
        return null;
    }
}

function chunkArray(array, size) {
    const chunks = [];
    for (let i = 0; i < array.length; i += size) {
        chunks.push(array.slice(i, i + size));
    }
    return chunks;
}

module.exports = {
    EmbedHelper,
    MessageFormatter,
    TimeParser,
    ComponentHelper,
    safeSend,
    safeEdit,
    safeReply,
    chunkArray
};

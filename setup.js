#!/usr/bin/env node

const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const { execSync } = require('child_process');

class ChatterBotSetup {
    constructor() {
        this.projectRoot = __dirname;
    }

    async run() {
        console.log(chalk.blue.bold('🤖 ChatterBot Setup Script'));
        console.log(chalk.blue('=' .repeat(40)));

        try {
            await this.checkNodeVersion();
            await this.createDirectories();
            await this.createEnvFile();
            await this.installDependencies();
            await this.validateConfiguration();
            await this.createDeploymentFiles();
            
            this.showCompletionMessage();
        } catch (error) {
            console.error(chalk.red('❌ Setup failed:'), error.message);
            process.exit(1);
        }
    }

    async checkNodeVersion() {
        const nodeVersion = process.version;
        const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);

        if (majorVersion < 16) {
            throw new Error(`Node.js 16 or higher is required. Current version: ${nodeVersion}`);
        }

        console.log(chalk.green(`✅ Node.js version: ${nodeVersion}`));
    }

    async createDirectories() {
        const directories = ['data', 'logs'];
        
        for (const dir of directories) {
            await fs.ensureDir(path.join(this.projectRoot, dir));
        }
        
        console.log(chalk.green('✅ Created necessary directories'));
    }

    async createEnvFile() {
        const envPath = path.join(this.projectRoot, '.env');
        const envExamplePath = path.join(this.projectRoot, '.env.example');

        if (await fs.pathExists(envPath)) {
            console.log(chalk.green('✅ .env file already exists'));
            return;
        }

        if (await fs.pathExists(envExamplePath)) {
            await fs.copy(envExamplePath, envPath);
            console.log(chalk.green('✅ Created .env file from template'));
            console.log(chalk.yellow('📝 Please edit .env with your bot token and configuration'));
        } else {
            console.log(chalk.red('❌ .env.example not found'));
        }
    }

    async installDependencies() {
        console.log(chalk.blue('📦 Installing dependencies...'));
        
        try {
            execSync('npm install', { 
                stdio: 'inherit',
                cwd: this.projectRoot 
            });
            console.log(chalk.green('✅ Dependencies installed successfully'));
        } catch (error) {
            throw new Error('Failed to install dependencies. Please run "npm install" manually.');
        }
    }

    async validateConfiguration() {
        console.log(chalk.blue('🔧 Validating configuration...'));
        
        try {
            require('dotenv').config();
            
            const token = process.env.DISCORD_TOKEN;
            if (!token) {
                console.log(chalk.yellow('⚠️ DISCORD_TOKEN not found in .env file'));
                console.log(chalk.yellow('Please add your Discord bot token to the .env file'));
            } else if (!this.isValidBotToken(token)) {
                console.log(chalk.yellow('⚠️ Bot token format looks incorrect'));
            } else {
                console.log(chalk.green('✅ Bot token found and appears valid'));
            }

            const openrouterApiKey = process.env.OPENROUTER_API_KEY;
            if (!openrouterApiKey) {
                console.log(chalk.yellow('⚠️ OPENROUTER_API_KEY not found - AI features will be disabled'));
            } else {
                console.log(chalk.green('✅ OpenRouter API key found'));
            }

        } catch (error) {
            console.log(chalk.yellow('⚠️ Could not validate configuration'));
        }
    }

    isValidBotToken(token) {
        // Basic Discord bot token validation
        return token.length > 50 && (
            token.startsWith('Bot ') ||
            token.match(/^[A-Za-z0-9._-]+$/)
        );
    }

    async createDeploymentFiles() {
        console.log(chalk.blue('🚀 Creating deployment files...'));

        // Create systemd service file
        const serviceContent = `[Unit]
Description=ChatterBot ModMail
After=network.target

[Service]
Type=simple
User=${process.env.USER || 'chatterbot'}
WorkingDirectory=${this.projectRoot}
ExecStart=${process.execPath} index.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production

[Install]
WantedBy=multi-user.target
`;

        await fs.writeFile(path.join(this.projectRoot, 'chatterbot.service'), serviceContent);

        // Create Docker files
        const dockerfileContent = `FROM node:18-alpine

WORKDIR /app

# Install dependencies
COPY package*.json ./
RUN npm ci --only=production

# Copy application code
COPY . .

# Create data directory
RUN mkdir -p data logs

# Run the bot
CMD ["node", "index.js"]
`;

        const dockerComposeContent = `version: '3.8'

services:
  chatterbot:
    build: .
    container_name: chatterbot
    restart: unless-stopped
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./.env:/app/.env:ro
    environment:
      - NODE_ENV=production
`;

        await fs.writeFile(path.join(this.projectRoot, 'Dockerfile'), dockerfileContent);
        await fs.writeFile(path.join(this.projectRoot, 'docker-compose.yml'), dockerComposeContent);

        // Create PM2 ecosystem file
        const pm2Config = {
            apps: [{
                name: 'chatterbot',
                script: 'index.js',
                instances: 1,
                autorestart: true,
                watch: false,
                max_memory_restart: '1G',
                env: {
                    NODE_ENV: 'production'
                }
            }]
        };

        await fs.writeJson(path.join(this.projectRoot, 'ecosystem.config.js'), pm2Config, { spaces: 2 });

        console.log(chalk.green('✅ Created deployment files'));
        console.log(chalk.blue('   • chatterbot.service (systemd)'));
        console.log(chalk.blue('   • Dockerfile & docker-compose.yml'));
        console.log(chalk.blue('   • ecosystem.config.js (PM2)'));
    }

    showCompletionMessage() {
        console.log(chalk.green.bold('\n✅ Setup completed successfully!'));
        console.log(chalk.blue('\n📋 Next steps:'));
        console.log(chalk.white('1. Edit .env file with your bot token and configuration'));
        console.log(chalk.white('2. Create a Discord application and bot at https://discord.com/developers/applications'));
        console.log(chalk.white('3. Get an OpenRouter API key from https://openrouter.ai for AI features'));
        console.log(chalk.white('4. Invite the bot to your server with appropriate permissions'));
        console.log(chalk.white('5. Run: npm start'));

        console.log(chalk.blue('\n🚀 Deployment options:'));
        console.log(chalk.white('• Development: npm run dev'));
        console.log(chalk.white('• Production: npm start'));
        console.log(chalk.white('• Docker: docker-compose up -d'));
        console.log(chalk.white('• PM2: pm2 start ecosystem.config.js'));
        console.log(chalk.white('• Systemd: sudo cp chatterbot.service /etc/systemd/system/'));

        console.log(chalk.blue('\n📚 For detailed setup instructions, see README.md'));
        console.log(chalk.green('\n🎉 Happy moderating with ChatterBot!'));
    }
}

// Run setup if called directly
if (require.main === module) {
    const setup = new ChatterBotSetup();
    setup.run().catch(console.error);
}

module.exports = ChatterBotSetup;

import discord
from discord.ext import commands
import openai
import asyncio
from typing import Optional, List

from database import db
from config import config
from utils.permissions import can_manage_tickets
from utils.helpers import EmbedBuilder, safe_send

class AIFeatures(commands.Cog):
    """AI-powered features for ChatterBot"""
    
    def __init__(self, bot):
        self.bot = bot
        self.openai_client = None
        self._setup_openai()
    
    def _setup_openai(self):
        """Initialize OpenAI client if API key is available"""
        api_key = config.get('ai_features.openai_api_key')
        if api_key and config.get('ai_features.enabled'):
            try:
                self.openai_client = openai.OpenAI(api_key=api_key)
            except Exception as e:
                print(f"Failed to initialize OpenAI client: {e}")
    
    @commands.command(name='aireply', aliases=['ai'])
    @can_manage_tickets()
    async def ai_reply(self, ctx, *, prompt: str = None):
        """Generate an AI-powered reply for the current ticket"""
        if not self.openai_client:
            embed = EmbedBuilder.error(
                "AI Not Available",
                "AI features are not enabled or configured."
            )
            await ctx.send(embed=embed)
            return
        
        # Check if this is a ticket channel
        ticket = await db.get_ticket(ctx.channel.id)
        if not ticket:
            embed = EmbedBuilder.error(
                "Not a Ticket",
                "This command can only be used in ticket channels."
            )
            await ctx.send(embed=embed)
            return
        
        # Get recent messages for context
        messages = []
        async for message in ctx.channel.history(limit=10):
            if not message.author.bot or message.embeds:
                # Include user messages and staff messages
                role = "user" if message.author.id == ticket['user_id'] else "assistant"
                content = message.content
                
                # Extract content from embeds if it's a forwarded message
                if message.embeds and message.author.bot:
                    embed = message.embeds[0]
                    if embed.description:
                        content = embed.description
                
                if content:
                    messages.append({"role": role, "content": content})
        
        # Reverse to get chronological order
        messages.reverse()
        
        # Add system prompt
        system_prompt = (
            "You are a helpful customer support assistant. "
            "Provide professional, empathetic, and helpful responses to customer inquiries. "
            "Keep responses concise but thorough. "
            "If you need more information, ask clarifying questions."
        )
        
        if prompt:
            system_prompt += f" Additional context: {prompt}"
        
        conversation = [{"role": "system", "content": system_prompt}] + messages
        
        # Show typing indicator
        async with ctx.typing():
            try:
                response = await self._generate_ai_response(conversation)
                
                if response:
                    # Create embed for AI response
                    embed = discord.Embed(
                        title="🤖 AI-Generated Response",
                        description=response,
                        color=discord.Color.purple(),
                        timestamp=ctx.message.created_at
                    )
                    embed.set_footer(text="Review and edit before sending to user")
                    
                    # Add buttons for actions
                    view = AIResponseView(response, ticket['user_id'])
                    await ctx.send(embed=embed, view=view)
                    
                else:
                    embed = EmbedBuilder.error(
                        "AI Error",
                        "Failed to generate AI response. Please try again."
                    )
                    await ctx.send(embed=embed)
                    
            except Exception as e:
                embed = EmbedBuilder.error(
                    "AI Error",
                    f"An error occurred while generating the response: {str(e)}"
                )
                await ctx.send(embed=embed)
    
    async def _generate_ai_response(self, conversation: List[dict]) -> Optional[str]:
        """Generate AI response using OpenAI"""
        try:
            response = await asyncio.to_thread(
                self.openai_client.chat.completions.create,
                model=config.get('ai_features.model', 'gpt-3.5-turbo'),
                messages=conversation,
                max_tokens=config.get('ai_features.max_tokens', 500),
                temperature=config.get('ai_features.temperature', 0.7)
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            print(f"OpenAI API error: {e}")
            return None
    
    @commands.command(name='summarize')
    @can_manage_tickets()
    async def summarize_ticket(self, ctx):
        """Generate an AI summary of the current ticket"""
        if not self.openai_client:
            embed = EmbedBuilder.error(
                "AI Not Available",
                "AI features are not enabled or configured."
            )
            await ctx.send(embed=embed)
            return
        
        ticket = await db.get_ticket(ctx.channel.id)
        if not ticket:
            embed = EmbedBuilder.error(
                "Not a Ticket",
                "This command can only be used in ticket channels."
            )
            await ctx.send(embed=embed)
            return
        
        # Get all messages in the ticket
        messages = []
        async for message in ctx.channel.history(limit=None, oldest_first=True):
            if message.content or message.embeds:
                content = message.content
                
                # Extract content from embeds
                if message.embeds and message.author.bot:
                    embed = message.embeds[0]
                    if embed.description:
                        content = embed.description
                
                if content:
                    author = "User" if message.author.id == ticket['user_id'] else "Staff"
                    messages.append(f"{author}: {content}")
        
        if not messages:
            embed = EmbedBuilder.warning(
                "No Messages",
                "No messages found to summarize."
            )
            await ctx.send(embed=embed)
            return
        
        # Create conversation for summarization
        conversation_text = "\n".join(messages)
        
        system_prompt = (
            "Summarize the following customer support conversation. "
            "Include the main issue, key points discussed, and current status. "
            "Keep it concise but comprehensive."
        )
        
        conversation = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": conversation_text}
        ]
        
        async with ctx.typing():
            summary = await self._generate_ai_response(conversation)
            
            if summary:
                embed = EmbedBuilder.info("🤖 Ticket Summary", summary)
                embed.set_footer(text="AI-generated summary")
                await ctx.send(embed=embed)
            else:
                embed = EmbedBuilder.error(
                    "Summary Failed",
                    "Failed to generate ticket summary."
                )
                await ctx.send(embed=embed)
    
    @commands.command(name='translate')
    @can_manage_tickets()
    async def translate_message(self, ctx, target_language: str, *, text: str = None):
        """Translate text using AI"""
        if not self.openai_client:
            embed = EmbedBuilder.error(
                "AI Not Available",
                "AI features are not enabled or configured."
            )
            await ctx.send(embed=embed)
            return
        
        # If no text provided, try to get from replied message
        if not text and ctx.message.reference:
            try:
                replied_message = await ctx.channel.fetch_message(ctx.message.reference.message_id)
                text = replied_message.content
                
                # Extract from embed if it's a forwarded message
                if replied_message.embeds and replied_message.author.bot:
                    embed = replied_message.embeds[0]
                    if embed.description:
                        text = embed.description
                        
            except discord.NotFound:
                pass
        
        if not text:
            embed = EmbedBuilder.error(
                "No Text",
                "Please provide text to translate or reply to a message."
            )
            await ctx.send(embed=embed)
            return
        
        # Create translation prompt
        system_prompt = f"Translate the following text to {target_language}. Only provide the translation, no additional text."
        
        conversation = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": text}
        ]
        
        async with ctx.typing():
            translation = await self._generate_ai_response(conversation)
            
            if translation:
                embed = discord.Embed(
                    title=f"🌐 Translation to {target_language.title()}",
                    color=discord.Color.blue()
                )
                embed.add_field(name="Original", value=text[:1000], inline=False)
                embed.add_field(name="Translation", value=translation[:1000], inline=False)
                await ctx.send(embed=embed)
            else:
                embed = EmbedBuilder.error(
                    "Translation Failed",
                    "Failed to translate the text."
                )
                await ctx.send(embed=embed)

class AIResponseView(discord.ui.View):
    """View for AI response actions"""
    
    def __init__(self, response: str, user_id: int):
        super().__init__(timeout=300)
        self.response = response
        self.user_id = user_id
    
    @discord.ui.button(label='Send to User', style=discord.ButtonStyle.green, emoji='📤')
    async def send_response(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Send the AI response to the user"""
        user = interaction.guild.get_member(self.user_id)
        if not user:
            await interaction.response.send_message("❌ User not found.", ephemeral=True)
            return
        
        # Send response to user
        embed = discord.Embed(
            description=self.response,
            color=discord.Color.green(),
            timestamp=interaction.created_at
        )
        embed.set_author(name="Support Team", icon_url=interaction.guild.icon.url if interaction.guild.icon else None)
        
        try:
            await user.send(embed=embed)
            await interaction.response.send_message("✅ Response sent to user!", ephemeral=True)
            
            # Disable the view
            for item in self.children:
                item.disabled = True
            await interaction.edit_original_response(view=self)
            
        except discord.Forbidden:
            await interaction.response.send_message("❌ Cannot send DM to user.", ephemeral=True)
    
    @discord.ui.button(label='Edit Response', style=discord.ButtonStyle.secondary, emoji='✏️')
    async def edit_response(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Open modal to edit the response"""
        modal = EditResponseModal(self.response)
        await interaction.response.send_modal(modal)
    
    @discord.ui.button(label='Discard', style=discord.ButtonStyle.danger, emoji='🗑️')
    async def discard_response(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Discard the AI response"""
        await interaction.response.send_message("🗑️ AI response discarded.", ephemeral=True)
        
        # Disable the view
        for item in self.children:
            item.disabled = True
        await interaction.edit_original_response(view=self)

class EditResponseModal(discord.ui.Modal, title='Edit AI Response'):
    """Modal for editing AI responses"""
    
    def __init__(self, original_response: str):
        super().__init__()
        self.response.default = original_response
    
    response = discord.ui.TextInput(
        label='Response',
        style=discord.TextStyle.paragraph,
        required=True,
        max_length=2000
    )
    
    async def on_submit(self, interaction: discord.Interaction):
        """Handle edited response"""
        embed = discord.Embed(
            title="✏️ Edited AI Response",
            description=self.response.value,
            color=discord.Color.blue()
        )
        embed.set_footer(text="Edited response - ready to send")
        
        # Create new view with edited response
        view = AIResponseView(self.response.value, 0)  # User ID would need to be passed
        await interaction.response.send_message(embed=embed, view=view, ephemeral=True)

async def setup(bot):
    await bot.add_cog(AIFeatures(bot))

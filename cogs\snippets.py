import discord
from discord.ext import commands
from typing import Optional, List

from database import db
from utils.permissions import can_manage_tickets
from utils.helpers import Embed<PERSON>uilder, PaginationView

class SnippetModal(discord.ui.Modal, title='Create Snippet'):
    """Modal for creating snippets"""
    
    def __init__(self):
        super().__init__()
    
    name = discord.ui.TextInput(
        label='Snippet Name',
        placeholder='Enter a unique name for this snippet...',
        required=True,
        max_length=50
    )
    
    category = discord.ui.TextInput(
        label='Category',
        placeholder='general, support, rules, etc.',
        required=False,
        max_length=30,
        default='general'
    )
    
    content = discord.ui.TextInput(
        label='Snippet Content',
        placeholder='Enter the snippet content...',
        required=True,
        style=discord.TextStyle.paragraph,
        max_length=2000
    )
    
    async def on_submit(self, interaction: discord.Interaction):
        """Handle snippet creation"""
        success = await db.create_snippet(
            self.name.value.lower(),
            self.content.value,
            interaction.user.id,
            self.category.value or 'general'
        )
        
        if success:
            embed = EmbedBuilder.success(
                "Snippet Created",
                f"Snippet `{self.name.value}` has been created successfully!"
            )
        else:
            embed = EmbedBuilder.error(
                "Snippet Exists",
                f"A snippet with the name `{self.name.value}` already exists."
            )
        
        await interaction.response.send_message(embed=embed, ephemeral=True)

class Snippets(commands.Cog):
    """Snippet management for quick responses"""
    
    def __init__(self, bot):
        self.bot = bot
    
    @commands.group(name='snippet', aliases=['s'], invoke_without_command=True)
    @can_manage_tickets()
    async def snippet(self, ctx, name: str = None):
        """Use a snippet or show snippet help"""
        if name is None:
            embed = EmbedBuilder.info(
                "Snippet Commands",
                "Use snippets for quick responses in tickets!"
            )
            embed.add_field(
                name="Usage",
                value="`!snippet <name>` - Use a snippet\n"
                      "`!snippet list` - List all snippets\n"
                      "`!snippet create` - Create a new snippet\n"
                      "`!snippet delete <name>` - Delete a snippet\n"
                      "`!snippet edit <name>` - Edit a snippet",
                inline=False
            )
            await ctx.send(embed=embed)
            return
        
        # Get and use snippet
        snippet = await db.get_snippet(name.lower())
        if snippet:
            # Send snippet content
            await ctx.send(snippet['content'])
            
            # Delete the command message
            try:
                await ctx.message.delete()
            except discord.NotFound:
                pass
        else:
            embed = EmbedBuilder.error(
                "Snippet Not Found",
                f"No snippet found with the name `{name}`."
            )
            await ctx.send(embed=embed, delete_after=5)
    
    @snippet.command(name='list', aliases=['ls'])
    @can_manage_tickets()
    async def list_snippets(self, ctx, category: str = None):
        """List all snippets"""
        snippets = await db.list_snippets(category)
        
        if not snippets:
            if category:
                embed = EmbedBuilder.warning(
                    "No Snippets",
                    f"No snippets found in category `{category}`."
                )
            else:
                embed = EmbedBuilder.warning(
                    "No Snippets",
                    "No snippets have been created yet."
                )
            await ctx.send(embed=embed)
            return
        
        # Group snippets by category
        categories = {}
        for snippet in snippets:
            cat = snippet['category']
            if cat not in categories:
                categories[cat] = []
            categories[cat].append(snippet)
        
        # Create embeds for pagination
        embeds = []
        for cat, cat_snippets in categories.items():
            embed = EmbedBuilder.info(f"Snippets - {cat.title()}", "")
            
            snippet_list = []
            for snippet in cat_snippets:
                snippet_list.append(f"`{snippet['name']}` (used {snippet['usage_count']} times)")
            
            embed.description = "\n".join(snippet_list)
            embed.set_footer(text=f"Total: {len(cat_snippets)} snippets")
            embeds.append(embed)
        
        if len(embeds) == 1:
            await ctx.send(embed=embeds[0])
        else:
            view = PaginationView(embeds)
            await ctx.send(embed=embeds[0], view=view)
    
    @snippet.command(name='create', aliases=['add', 'new'])
    @can_manage_tickets()
    async def create_snippet(self, ctx):
        """Create a new snippet"""
        modal = SnippetModal()
        await ctx.interaction.response.send_modal(modal) if ctx.interaction else await ctx.send("This command requires slash command interaction.")
    
    @snippet.command(name='delete', aliases=['del', 'remove'])
    @can_manage_tickets()
    async def delete_snippet(self, ctx, name: str):
        """Delete a snippet"""
        # Check if snippet exists
        snippet = await db.get_snippet(name.lower())
        if not snippet:
            embed = EmbedBuilder.error(
                "Snippet Not Found",
                f"No snippet found with the name `{name}`."
            )
            await ctx.send(embed=embed)
            return
        
        # Confirmation view
        view = ConfirmationView(ctx.author.id)
        embed = EmbedBuilder.warning(
            "Confirm Deletion",
            f"Are you sure you want to delete the snippet `{name}`?"
        )
        
        message = await ctx.send(embed=embed, view=view)
        await view.wait()
        
        if view.confirmed:
            # Delete snippet (would need database method)
            embed = EmbedBuilder.success(
                "Snippet Deleted",
                f"Snippet `{name}` has been deleted."
            )
        else:
            embed = EmbedBuilder.info(
                "Deletion Cancelled",
                f"Snippet `{name}` was not deleted."
            )
        
        await message.edit(embed=embed, view=None)
    
    @snippet.command(name='info', aliases=['show'])
    @can_manage_tickets()
    async def snippet_info(self, ctx, name: str):
        """Show detailed information about a snippet"""
        snippet = await db.get_snippet(name.lower())
        if not snippet:
            embed = EmbedBuilder.error(
                "Snippet Not Found",
                f"No snippet found with the name `{name}`."
            )
            await ctx.send(embed=embed)
            return
        
        embed = EmbedBuilder.info(f"Snippet: {snippet['name']}", "")
        embed.add_field(name="Category", value=snippet['category'], inline=True)
        embed.add_field(name="Usage Count", value=snippet['usage_count'], inline=True)
        embed.add_field(name="Created", value=f"<t:{int(snippet['created_at'])}:R>", inline=True)
        embed.add_field(name="Content", value=snippet['content'][:1000], inline=False)
        
        if len(snippet['content']) > 1000:
            embed.set_footer(text="Content truncated...")
        
        await ctx.send(embed=embed)
    
    @snippet.command(name='search')
    @can_manage_tickets()
    async def search_snippets(self, ctx, *, query: str):
        """Search snippets by name or content"""
        snippets = await db.list_snippets()
        
        # Filter snippets by query
        matching_snippets = []
        query_lower = query.lower()
        
        for snippet in snippets:
            if (query_lower in snippet['name'].lower() or 
                query_lower in snippet['content'].lower()):
                matching_snippets.append(snippet)
        
        if not matching_snippets:
            embed = EmbedBuilder.warning(
                "No Results",
                f"No snippets found matching `{query}`."
            )
            await ctx.send(embed=embed)
            return
        
        embed = EmbedBuilder.info(f"Search Results for '{query}'", "")
        
        results = []
        for snippet in matching_snippets[:10]:  # Limit to 10 results
            results.append(f"`{snippet['name']}` - {snippet['content'][:50]}...")
        
        embed.description = "\n".join(results)
        embed.set_footer(text=f"Showing {len(results)} of {len(matching_snippets)} results")
        
        await ctx.send(embed=embed)

class ConfirmationView(discord.ui.View):
    """Simple confirmation view"""
    
    def __init__(self, user_id: int):
        super().__init__(timeout=30)
        self.user_id = user_id
        self.confirmed = None
    
    @discord.ui.button(label='Confirm', style=discord.ButtonStyle.danger)
    async def confirm(self, interaction: discord.Interaction, button: discord.ui.Button):
        if interaction.user.id != self.user_id:
            await interaction.response.send_message("You can't use this button.", ephemeral=True)
            return
        
        self.confirmed = True
        self.stop()
        await interaction.response.defer()
    
    @discord.ui.button(label='Cancel', style=discord.ButtonStyle.secondary)
    async def cancel(self, interaction: discord.Interaction, button: discord.ui.Button):
        if interaction.user.id != self.user_id:
            await interaction.response.send_message("You can't use this button.", ephemeral=True)
            return
        
        self.confirmed = False
        self.stop()
        await interaction.response.defer()

async def setup(bot):
    await bot.add_cog(Snippets(bot))

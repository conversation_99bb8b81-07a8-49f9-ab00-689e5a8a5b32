const config = require('../config/config');

class PermissionManager {
    static hasStaffRole(member) {
        const staffRoleId = config.get('permissions.staff_role_id');
        if (!staffRoleId) return false;
        
        return member.roles.cache.has(staffRoleId);
    }

    static hasAdminRole(member) {
        const adminRoleId = config.get('permissions.admin_role_id');
        if (!adminRoleId) {
            return member.permissions.has('ADMINISTRATOR');
        }
        
        return member.roles.cache.has(adminRoleId);
    }

    static hasModeratorRole(member) {
        const modRoleId = config.get('permissions.moderator_role_id');
        if (!modRoleId) {
            return this.hasStaffRole(member);
        }
        
        return member.roles.cache.has(modRoleId);
    }

    static hasSupportRole(member) {
        const supportRoleId = config.get('permissions.support_role_id');
        if (!supportRoleId) {
            return this.hasStaffRole(member);
        }
        
        return member.roles.cache.has(supportRoleId);
    }

    static canManageTickets(member) {
        return this.hasStaffRole(member) || 
               this.hasModeratorRole(member) || 
               this.hasAdminRole(member);
    }

    static canUseAdminCommands(member) {
        return this.hasAdminRole(member);
    }

    static canManageSnippets(member) {
        return this.canManageTickets(member);
    }

    static canBlacklistUsers(member) {
        return this.hasAdminRole(member) || this.hasModeratorRole(member);
    }
}

// Middleware functions for command checking
const requireStaff = (interaction) => {
    if (!interaction.member) return false;
    return PermissionManager.hasStaffRole(interaction.member);
};

const requireAdmin = (interaction) => {
    if (!interaction.member) return false;
    return PermissionManager.hasAdminRole(interaction.member);
};

const requireModerator = (interaction) => {
    if (!interaction.member) return false;
    return PermissionManager.hasModeratorRole(interaction.member);
};

const requireTicketManager = (interaction) => {
    if (!interaction.member) return false;
    return PermissionManager.canManageTickets(interaction.member);
};

module.exports = {
    PermissionManager,
    requireStaff,
    requireAdmin,
    requireModerator,
    requireTicketManager
};

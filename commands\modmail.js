const { Slash<PERSON><PERSON>mandB<PERSON>er, ChannelType, PermissionFlagsBits } = require('discord.js');
const database = require('../database/database');
const config = require('../config/config');
const { Embed<PERSON><PERSON>per, MessageFormatter, ComponentHelper, safeSend, safeReply } = require('../utils/helpers');
const { PermissionManager } = require('../utils/permissions');

class ModMailHandler {
    constructor(client) {
        this.client = client;
        this.activeTickets = new Map(); // Cache for active tickets
    }

    async handleDM(message) {
        const user = message.author;

        // Check if user is blacklisted
        if (await database.isBlacklisted(user.id)) {
            const embed = EmbedHelper.error('Blacklisted', config.get('messages.blacklist_message'));
            await safeSend(user, { embeds: [embed] });
            return;
        }

        // Check if user has too many open tickets
        const openTickets = await database.getUserTickets(user.id, 'open');
        const maxTickets = config.get('modmail.max_tickets_per_user', 3);

        if (openTickets.length >= maxTickets) {
            const messageText = MessageFormatter.formatMessage(
                config.get('messages.max_tickets_message'),
                { max_tickets: maxTickets }
            );
            const embed = EmbedHelper.warning('Too Many Tickets', messageText);
            await safeSend(user, { embeds: [embed] });
            return;
        }

        // Check if user already has an open ticket
        const existingTicket = openTickets.find(ticket => ticket.status === 'open');

        if (existingTicket) {
            // Forward message to existing ticket
            const channel = this.client.channels.cache.get(existingTicket.channel_id);
            if (channel) {
                await this.forwardUserMessage(message, channel, existingTicket.id);
                await database.updateTicketActivity(channel.id);
            }
        } else {
            // Create new ticket
            await this.createTicket(user, message);
        }
    }

    async createTicket(user, initialMessage) {
        const guild = this.client.guilds.cache.get(config.guildId);
        if (!guild) return;

        const category = guild.channels.cache.get(config.get('modmail.category_id'));
        if (!category) {
            const embed = EmbedHelper.error('Configuration Error', 'ModMail category not found.');
            await safeSend(user, { embeds: [embed] });
            return;
        }

        // Create ticket channel
        const channelName = `ticket-${user.username}-${user.discriminator}`;

        try {
            const channel = await guild.channels.create({
                name: channelName,
                type: ChannelType.GuildText,
                parent: category.id,
                topic: `Support ticket for ${user.tag} (${user.id})`
            });

            // Create ticket in database
            const ticketId = await database.createTicket(user.id, channel.id, guild.id);

            // Send welcome message to user
            const welcomeMsg = MessageFormatter.formatMessage(
                config.get('messages.welcome_message'),
                { username: user.username, user: user.toString() }
            );
            const embed = EmbedHelper.success('Ticket Created', welcomeMsg);
            await safeSend(user, { embeds: [embed] });

            // Set up ticket channel
            await this.setupTicketChannel(channel, user, ticketId, initialMessage);

            // Log ticket creation
            const logChannel = guild.channels.cache.get(config.get('modmail.log_channel_id'));
            if (logChannel) {
                const logEmbed = EmbedHelper.ticketCreated(user, ticketId);
                await safeSend(logChannel, { embeds: [logEmbed] });
            }

        } catch (error) {
            console.error('Error creating ticket:', error);
            const embed = EmbedHelper.error('Permission Error', 'Bot lacks permission to create channels.');
            await safeSend(user, { embeds: [embed] });
        }
    }

    async setupTicketChannel(channel, user, ticketId, initialMessage) {
        // Create ticket info embed
        const embed = EmbedHelper.ticketCreated(user, ticketId);
        embed.addFields({ name: 'Initial Message', value: initialMessage.content.substring(0, 1000), inline: false });

        // Add ticket management buttons
        const buttons = ComponentHelper.createTicketButtons(ticketId);

        await channel.send({ embeds: [embed], components: [buttons] });

        // Forward the initial message
        await this.forwardUserMessage(initialMessage, channel, ticketId);

        // Ping staff role if configured
        const staffRoleId = config.get('permissions.staff_role_id');
        if (staffRoleId) {
            const staffRole = channel.guild.roles.cache.get(staffRoleId);
            if (staffRole) {
                await channel.send(`${staffRole} New ticket created!`);
            }
        }
    }

    async forwardUserMessage(message, channel, ticketId) {
        // Create embed for user message
        const embed = new EmbedBuilder()
            .setDescription(message.content)
            .setColor(0x0099ff)
            .setAuthor({ name: message.author.tag, iconURL: message.author.displayAvatarURL() })
            .setTimestamp(message.createdAt);

        // Handle attachments
        if (message.attachments.size > 0) {
            const attachmentText = message.attachments.map(att => `[${att.name}](${att.url})`).join('\n');
            embed.addFields({ name: 'Attachments', value: attachmentText, inline: false });
        }

        await channel.send({ embeds: [embed] });

        // Log message to database
        await database.logMessage(
            ticketId,
            message.id,
            message.author.id,
            message.content,
            message.attachments.map(att => att.url)
        );
    }

    async handleTicketMessage(message) {
        // Get ticket info
        const ticket = await database.getTicket(message.channel.id);
        if (!ticket) return;

        // Skip if message is from the ticket user (already handled in DM)
        if (message.author.id === ticket.user_id) return;

        // Check if sender is staff
        if (!PermissionManager.canManageTickets(message.member)) return;

        // Forward staff message to user
        const user = this.client.users.cache.get(ticket.user_id);
        if (user) {
            await this.forwardStaffMessage(message, user, ticket.id);
            await database.updateTicketActivity(message.channel.id);
        }
    }

    async forwardStaffMessage(message, user, ticketId) {
        // Create embed for staff message
        const embed = new EmbedBuilder()
            .setDescription(message.content)
            .setColor(0x00ff00)
            .setAuthor({ 
                name: 'Support Team', 
                iconURL: message.guild.iconURL() || undefined 
            })
            .setTimestamp(message.createdAt);

        // Handle attachments
        if (message.attachments.size > 0) {
            const attachmentText = message.attachments.map(att => `[${att.name}](${att.url})`).join('\n');
            embed.addFields({ name: 'Attachments', value: attachmentText, inline: false });
        }

        await safeSend(user, { embeds: [embed] });

        // Log message to database
        await database.logMessage(
            ticketId,
            message.id,
            message.author.id,
            message.content,
            message.attachments.map(att => att.url),
            true // isStaff
        );
    }

    async closeTicket(interaction, reason = 'No reason provided', isAnonymous = false) {
        const ticket = await database.getTicket(interaction.channel.id);
        if (!ticket) {
            const embed = EmbedHelper.error('Not a Ticket', 'This command can only be used in ticket channels.');
            await safeReply(interaction, { embeds: [embed], ephemeral: true });
            return;
        }

        // Close the ticket
        const success = await database.closeTicket(interaction.channel.id, interaction.user.id, reason, isAnonymous);

        if (success) {
            // Notify user
            const user = this.client.users.cache.get(ticket.user_id);
            if (user) {
                const messageKey = isAnonymous ? 'anonymous_close_message' : 'close_message';
                const embed = EmbedHelper.info('Ticket Closed', config.get(`messages.${messageKey}`));
                if (reason !== 'No reason provided') {
                    embed.addFields({ name: 'Reason', value: reason, inline: false });
                }
                await safeSend(user, { embeds: [embed] });
            }

            // Send confirmation
            const embed = EmbedHelper.success('Ticket Closed', `Reason: ${reason}`);
            await safeReply(interaction, { embeds: [embed] });

            // Delete channel after delay
            setTimeout(async () => {
                try {
                    await interaction.channel.delete(`Ticket closed by ${interaction.user.tag}`);
                } catch (error) {
                    console.error('Error deleting ticket channel:', error);
                }
            }, 10000);
        } else {
            const embed = EmbedHelper.error('Close Failed', 'Failed to close ticket.');
            await safeReply(interaction, { embeds: [embed], ephemeral: true });
        }
    }
}

// Slash command definitions
const commands = [
    new SlashCommandBuilder()
        .setName('close')
        .setDescription('Close the current ticket')
        .addStringOption(option =>
            option.setName('reason')
                .setDescription('Reason for closing the ticket')
                .setRequired(false)
        ),
    
    new SlashCommandBuilder()
        .setName('aclose')
        .setDescription('Close the current ticket anonymously')
        .addStringOption(option =>
            option.setName('reason')
                .setDescription('Reason for closing the ticket')
                .setRequired(false)
        ),
    
    new SlashCommandBuilder()
        .setName('areply')
        .setDescription('Send an anonymous reply to the ticket')
        .addStringOption(option =>
            option.setName('message')
                .setDescription('Message to send anonymously')
                .setRequired(true)
        )
];

module.exports = {
    ModMailHandler,
    commands
};

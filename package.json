{"name": "chatterbot-js", "version": "1.0.0", "description": "Advanced ModMail Bot with AI-powered features using Puter.js", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "setup": "node setup.js", "test": "jest", "lint": "eslint .", "format": "prettier --write ."}, "keywords": ["discord", "bot", "modmail", "support", "ai", "puter", "tickets", "customer-service"], "author": "ChatterBot Team", "license": "MIT", "dependencies": {"discord.js": "^14.14.1", "sqlite3": "^5.1.6", "dotenv": "^16.3.1", "openai": "^4.20.1", "node-cron": "^3.0.3", "moment": "^2.29.4", "chalk": "^4.1.2", "fs-extra": "^11.2.0", "uuid": "^9.0.1", "axios": "^1.6.2"}, "devDependencies": {"nodemon": "^3.0.2", "eslint": "^8.56.0", "prettier": "^3.1.1", "jest": "^29.7.0"}, "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/chatterbot-js.git"}, "bugs": {"url": "https://github.com/your-username/chatterbot-js/issues"}, "homepage": "https://github.com/your-username/chatterbot-js#readme"}
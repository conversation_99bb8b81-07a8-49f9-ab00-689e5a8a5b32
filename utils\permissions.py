import discord
from discord.ext import commands
from typing import Union, List
from config import config

class PermissionManager:
    """Advanced permission management for ChatterBot"""
    
    @staticmethod
    def has_staff_role(member: discord.Member) -> bool:
        """Check if member has staff role"""
        staff_role_id = config.get('permissions.staff_role_id')
        if not staff_role_id:
            return False
        
        return any(role.id == staff_role_id for role in member.roles)
    
    @staticmethod
    def has_admin_role(member: discord.Member) -> bool:
        """Check if member has admin role"""
        admin_role_id = config.get('permissions.admin_role_id')
        if not admin_role_id:
            return member.guild_permissions.administrator
        
        return any(role.id == admin_role_id for role in member.roles)
    
    @staticmethod
    def has_moderator_role(member: discord.Member) -> bool:
        """Check if member has moderator role"""
        mod_role_id = config.get('permissions.moderator_role_id')
        if not mod_role_id:
            return PermissionManager.has_staff_role(member)
        
        return any(role.id == mod_role_id for role in member.roles)
    
    @staticmethod
    def has_support_role(member: discord.Member) -> bool:
        """Check if member has support role"""
        support_role_id = config.get('permissions.support_role_id')
        if not support_role_id:
            return PermissionManager.has_staff_role(member)
        
        return any(role.id == support_role_id for role in member.roles)
    
    @staticmethod
    def can_manage_tickets(member: discord.Member) -> bool:
        """Check if member can manage tickets"""
        return (PermissionManager.has_staff_role(member) or 
                PermissionManager.has_moderator_role(member) or
                PermissionManager.has_admin_role(member))
    
    @staticmethod
    def can_use_admin_commands(member: discord.Member) -> bool:
        """Check if member can use admin commands"""
        return PermissionManager.has_admin_role(member)
    
    @staticmethod
    def can_manage_snippets(member: discord.Member) -> bool:
        """Check if member can manage snippets"""
        return PermissionManager.can_manage_tickets(member)
    
    @staticmethod
    def can_blacklist_users(member: discord.Member) -> bool:
        """Check if member can blacklist users"""
        return (PermissionManager.has_admin_role(member) or 
                PermissionManager.has_moderator_role(member))

def is_staff():
    """Decorator to check if user has staff permissions"""
    async def predicate(ctx):
        if not isinstance(ctx.author, discord.Member):
            return False
        return PermissionManager.has_staff_role(ctx.author)
    return commands.check(predicate)

def is_admin():
    """Decorator to check if user has admin permissions"""
    async def predicate(ctx):
        if not isinstance(ctx.author, discord.Member):
            return False
        return PermissionManager.has_admin_role(ctx.author)
    return commands.check(predicate)

def is_moderator():
    """Decorator to check if user has moderator permissions"""
    async def predicate(ctx):
        if not isinstance(ctx.author, discord.Member):
            return False
        return PermissionManager.has_moderator_role(ctx.author)
    return commands.check(predicate)

def can_manage_tickets():
    """Decorator to check if user can manage tickets"""
    async def predicate(ctx):
        if not isinstance(ctx.author, discord.Member):
            return False
        return PermissionManager.can_manage_tickets(ctx.author)
    return commands.check(predicate)

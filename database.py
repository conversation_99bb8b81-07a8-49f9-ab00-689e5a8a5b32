import aiosqlite
import asyncio
import json
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional, List, Dict, Any, Tuple
from config import config

class Database:
    """Advanced database management for ChatterBot"""
    
    def __init__(self):
        self.db_path = config.database_path
        self._connection = None
    
    async def connect(self):
        """Initialize database connection and create tables"""
        self._connection = await aiosqlite.connect(self.db_path)
        await self._create_tables()
    
    async def close(self):
        """Close database connection"""
        if self._connection:
            await self._connection.close()
    
    async def _create_tables(self):
        """Create all necessary database tables"""
        
        # Tickets table
        await self._connection.execute("""
            CREATE TABLE IF NOT EXISTS tickets (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                channel_id INTEGER UNIQUE,
                guild_id INTEGER NOT NULL,
                status TEXT DEFAULT 'open',
                category TEXT DEFAULT 'general',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                closed_at TIMESTAMP,
                closed_by INTEGER,
                close_reason TEXT,
                is_anonymous_close BOOLEAN DEFAULT FALSE,
                scheduled_close_at TIMESTAMP,
                priority INTEGER DEFAULT 1,
                assigned_to INTEGER,
                last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Messages table for logging
        await self._connection.execute("""
            CREATE TABLE IF NOT EXISTS messages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ticket_id INTEGER,
                message_id INTEGER,
                user_id INTEGER NOT NULL,
                content TEXT,
                attachments TEXT,
                is_staff BOOLEAN DEFAULT FALSE,
                is_anonymous BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (ticket_id) REFERENCES tickets (id)
            )
        """)
        
        # Snippets table
        await self._connection.execute("""
            CREATE TABLE IF NOT EXISTS snippets (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                content TEXT NOT NULL,
                created_by INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                usage_count INTEGER DEFAULT 0,
                category TEXT DEFAULT 'general'
            )
        """)
        
        # User settings and blacklist
        await self._connection.execute("""
            CREATE TABLE IF NOT EXISTS users (
                user_id INTEGER PRIMARY KEY,
                is_blacklisted BOOLEAN DEFAULT FALSE,
                blacklist_reason TEXT,
                blacklisted_by INTEGER,
                blacklisted_at TIMESTAMP,
                total_tickets INTEGER DEFAULT 0,
                last_ticket_at TIMESTAMP,
                notes TEXT
            )
        """)
        
        # Staff actions log
        await self._connection.execute("""
            CREATE TABLE IF NOT EXISTS staff_actions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                staff_id INTEGER NOT NULL,
                action_type TEXT NOT NULL,
                target_id INTEGER,
                details TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Scheduled tasks
        await self._connection.execute("""
            CREATE TABLE IF NOT EXISTS scheduled_tasks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_type TEXT NOT NULL,
                target_id INTEGER NOT NULL,
                scheduled_for TIMESTAMP NOT NULL,
                data TEXT,
                completed BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Ticket categories
        await self._connection.execute("""
            CREATE TABLE IF NOT EXISTS ticket_categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                description TEXT,
                emoji TEXT,
                ping_role_id INTEGER,
                auto_response TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        await self._connection.commit()
    
    # Ticket operations
    async def create_ticket(self, user_id: int, channel_id: int, guild_id: int, 
                          category: str = "general") -> int:
        """Create a new ticket"""
        cursor = await self._connection.execute("""
            INSERT INTO tickets (user_id, channel_id, guild_id, category)
            VALUES (?, ?, ?, ?)
        """, (user_id, channel_id, guild_id, category))
        
        ticket_id = cursor.lastrowid
        
        # Update user stats
        await self._connection.execute("""
            INSERT OR REPLACE INTO users (user_id, total_tickets, last_ticket_at)
            VALUES (?, COALESCE((SELECT total_tickets FROM users WHERE user_id = ?), 0) + 1, ?)
        """, (user_id, user_id, datetime.now()))
        
        await self._connection.commit()
        return ticket_id
    
    async def get_ticket(self, channel_id: int) -> Optional[Dict[str, Any]]:
        """Get ticket by channel ID"""
        cursor = await self._connection.execute("""
            SELECT * FROM tickets WHERE channel_id = ? AND status = 'open'
        """, (channel_id,))
        
        row = await cursor.fetchone()
        if row:
            columns = [description[0] for description in cursor.description]
            return dict(zip(columns, row))
        return None
    
    async def get_user_tickets(self, user_id: int, status: str = "open") -> List[Dict[str, Any]]:
        """Get all tickets for a user"""
        cursor = await self._connection.execute("""
            SELECT * FROM tickets WHERE user_id = ? AND status = ?
            ORDER BY created_at DESC
        """, (user_id, status))
        
        rows = await cursor.fetchall()
        columns = [description[0] for description in cursor.description]
        return [dict(zip(columns, row)) for row in rows]
    
    async def close_ticket(self, channel_id: int, closed_by: int, reason: str = None, 
                         is_anonymous: bool = False) -> bool:
        """Close a ticket"""
        cursor = await self._connection.execute("""
            UPDATE tickets 
            SET status = 'closed', closed_at = ?, closed_by = ?, 
                close_reason = ?, is_anonymous_close = ?
            WHERE channel_id = ? AND status = 'open'
        """, (datetime.now(), closed_by, reason, is_anonymous, channel_id))
        
        await self._connection.commit()
        return cursor.rowcount > 0

    async def update_ticket_activity(self, channel_id: int):
        """Update last activity timestamp for a ticket"""
        await self._connection.execute("""
            UPDATE tickets SET last_activity = ? WHERE channel_id = ?
        """, (datetime.now(), channel_id))
        await self._connection.commit()

    # Message operations
    async def log_message(self, ticket_id: int, message_id: int, user_id: int,
                         content: str, attachments: List[str] = None,
                         is_staff: bool = False, is_anonymous: bool = False):
        """Log a message to the database"""
        attachments_json = json.dumps(attachments) if attachments else None

        await self._connection.execute("""
            INSERT INTO messages (ticket_id, message_id, user_id, content,
                                attachments, is_staff, is_anonymous)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (ticket_id, message_id, user_id, content, attachments_json, is_staff, is_anonymous))

        await self._connection.commit()

    # Snippet operations
    async def create_snippet(self, name: str, content: str, created_by: int,
                           category: str = "general") -> bool:
        """Create a new snippet"""
        try:
            await self._connection.execute("""
                INSERT INTO snippets (name, content, created_by, category)
                VALUES (?, ?, ?, ?)
            """, (name, content, created_by, category))
            await self._connection.commit()
            return True
        except aiosqlite.IntegrityError:
            return False

    async def get_snippet(self, name: str) -> Optional[Dict[str, Any]]:
        """Get a snippet by name"""
        cursor = await self._connection.execute("""
            SELECT * FROM snippets WHERE name = ?
        """, (name,))

        row = await cursor.fetchone()
        if row:
            columns = [description[0] for description in cursor.description]
            # Increment usage count
            await self._connection.execute("""
                UPDATE snippets SET usage_count = usage_count + 1 WHERE name = ?
            """, (name,))
            await self._connection.commit()
            return dict(zip(columns, row))
        return None

    async def list_snippets(self, category: str = None) -> List[Dict[str, Any]]:
        """List all snippets, optionally filtered by category"""
        if category:
            cursor = await self._connection.execute("""
                SELECT name, category, usage_count FROM snippets
                WHERE category = ? ORDER BY usage_count DESC
            """, (category,))
        else:
            cursor = await self._connection.execute("""
                SELECT name, category, usage_count FROM snippets
                ORDER BY usage_count DESC
            """)

        rows = await cursor.fetchall()
        columns = [description[0] for description in cursor.description]
        return [dict(zip(columns, row)) for row in rows]

    # User management
    async def blacklist_user(self, user_id: int, reason: str, blacklisted_by: int) -> bool:
        """Blacklist a user"""
        await self._connection.execute("""
            INSERT OR REPLACE INTO users
            (user_id, is_blacklisted, blacklist_reason, blacklisted_by, blacklisted_at)
            VALUES (?, TRUE, ?, ?, ?)
        """, (user_id, reason, blacklisted_by, datetime.now()))

        await self._connection.commit()
        return True

    async def unblacklist_user(self, user_id: int) -> bool:
        """Remove user from blacklist"""
        cursor = await self._connection.execute("""
            UPDATE users SET is_blacklisted = FALSE, blacklist_reason = NULL,
                           blacklisted_by = NULL, blacklisted_at = NULL
            WHERE user_id = ?
        """, (user_id,))

        await self._connection.commit()
        return cursor.rowcount > 0

    async def is_blacklisted(self, user_id: int) -> bool:
        """Check if user is blacklisted"""
        cursor = await self._connection.execute("""
            SELECT is_blacklisted FROM users WHERE user_id = ?
        """, (user_id,))

        row = await cursor.fetchone()
        return row[0] if row else False

    # Scheduled tasks
    async def schedule_task(self, task_type: str, target_id: int,
                          scheduled_for: datetime, data: Dict[str, Any] = None):
        """Schedule a task for later execution"""
        data_json = json.dumps(data) if data else None

        await self._connection.execute("""
            INSERT INTO scheduled_tasks (task_type, target_id, scheduled_for, data)
            VALUES (?, ?, ?, ?)
        """, (task_type, target_id, scheduled_for, data_json))

        await self._connection.commit()

    async def get_due_tasks(self) -> List[Dict[str, Any]]:
        """Get all tasks that are due for execution"""
        cursor = await self._connection.execute("""
            SELECT * FROM scheduled_tasks
            WHERE scheduled_for <= ? AND completed = FALSE
        """, (datetime.now(),))

        rows = await cursor.fetchall()
        columns = [description[0] for description in cursor.description]
        return [dict(zip(columns, row)) for row in rows]

    async def complete_task(self, task_id: int):
        """Mark a scheduled task as completed"""
        await self._connection.execute("""
            UPDATE scheduled_tasks SET completed = TRUE WHERE id = ?
        """, (task_id,))
        await self._connection.commit()

# Global database instance
db = Database()

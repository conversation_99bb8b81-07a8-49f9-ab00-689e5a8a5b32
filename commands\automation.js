const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, TextInputBuilder, TextInputStyle, ActionRowBuilder } = require('discord.js');
const database = require('../database/database');
const config = require('../config/config');
const { EmbedHelper, safeReply } = require('../utils/helpers');
const { requireAdmin } = require('../utils/permissions');

class AutomationManager {
    constructor(client) {
        this.client = client;
        this.autoResponders = new Map();
        this.loadAutoResponders();
    }

    async loadAutoResponders() {
        try {
            const responders = await database.all('SELECT * FROM auto_responders WHERE enabled = TRUE');
            for (const responder of responders) {
                this.autoResponders.set(responder.id, {
                    ...responder,
                    triggers: JSON.parse(responder.triggers),
                    conditions: JSON.parse(responder.conditions || '{}')
                });
            }
            console.log(`✅ Loaded ${responders.length} auto-responders`);
        } catch (error) {
            console.error('Error loading auto-responders:', error);
        }
    }

    async createAutoResponder(interaction) {
        const modal = new ModalBuilder()
            .setCustomId('create_autoresponder_modal')
            .setTitle('Create Auto-Responder');

        const nameInput = new TextInputBuilder()
            .setCustomId('responder_name')
            .setLabel('Responder Name')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('e.g., "Welcome Message"')
            .setRequired(true)
            .setMaxLength(50);

        const triggersInput = new TextInputBuilder()
            .setCustomId('responder_triggers')
            .setLabel('Trigger Keywords (comma separated)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('e.g., "hello, hi, help"')
            .setRequired(true)
            .setMaxLength(200);

        const responseInput = new TextInputBuilder()
            .setCustomId('responder_response')
            .setLabel('Auto Response Message')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('Enter the automatic response message...')
            .setRequired(true)
            .setMaxLength(2000);

        const conditionsInput = new TextInputBuilder()
            .setCustomId('responder_conditions')
            .setLabel('Conditions (JSON format, optional)')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('{"new_user_only": true, "business_hours": true}')
            .setRequired(false)
            .setMaxLength(500);

        const firstRow = new ActionRowBuilder().addComponents(nameInput);
        const secondRow = new ActionRowBuilder().addComponents(triggersInput);
        const thirdRow = new ActionRowBuilder().addComponents(responseInput);
        const fourthRow = new ActionRowBuilder().addComponents(conditionsInput);

        modal.addComponents(firstRow, secondRow, thirdRow, fourthRow);
        await interaction.showModal(modal);
    }

    async handleCreateAutoResponderModal(interaction) {
        const name = interaction.fields.getTextInputValue('responder_name');
        const triggersText = interaction.fields.getTextInputValue('responder_triggers');
        const response = interaction.fields.getTextInputValue('responder_response');
        const conditionsText = interaction.fields.getTextInputValue('responder_conditions') || '{}';

        try {
            const triggers = triggersText.split(',').map(t => t.trim().toLowerCase());
            const conditions = JSON.parse(conditionsText);

            const result = await database.run(`
                INSERT INTO auto_responders (name, triggers, response, conditions, created_by, enabled)
                VALUES (?, ?, ?, ?, ?, TRUE)
            `, [name, JSON.stringify(triggers), response, JSON.stringify(conditions), interaction.user.id]);

            // Add to memory
            this.autoResponders.set(result.id, {
                id: result.id,
                name,
                triggers,
                response,
                conditions,
                enabled: true
            });

            const embed = EmbedHelper.success(
                'Auto-Responder Created',
                `Auto-responder "${name}" has been created successfully!`
            );
            await safeReply(interaction, { embeds: [embed], ephemeral: true });

        } catch (error) {
            console.error('Error creating auto-responder:', error);
            const embed = EmbedHelper.error(
                'Creation Failed',
                'Failed to create auto-responder. Check your JSON conditions format.'
            );
            await safeReply(interaction, { embeds: [embed], ephemeral: true });
        }
    }

    async listAutoResponders(interaction) {
        const responders = Array.from(this.autoResponders.values());

        if (responders.length === 0) {
            const embed = EmbedHelper.info('No Auto-Responders', 'No auto-responders have been created yet.');
            await safeReply(interaction, { embeds: [embed], ephemeral: true });
            return;
        }

        const embed = new EmbedBuilder()
            .setTitle('🤖 Auto-Responders')
            .setColor(0x0099ff)
            .setTimestamp();

        const responderList = responders.map(r => {
            const status = r.enabled ? '🟢' : '🔴';
            const triggers = r.triggers.slice(0, 3).join(', ') + (r.triggers.length > 3 ? '...' : '');
            return `${status} **${r.name}**\nTriggers: ${triggers}`;
        }).join('\n\n');

        embed.setDescription(responderList);
        embed.setFooter({ text: `Total: ${responders.length} auto-responders` });

        await safeReply(interaction, { embeds: [embed], ephemeral: true });
    }

    async toggleAutoResponder(interaction) {
        const name = interaction.options.getString('name');
        const responder = Array.from(this.autoResponders.values()).find(r => 
            r.name.toLowerCase() === name.toLowerCase()
        );

        if (!responder) {
            const embed = EmbedHelper.error('Not Found', `Auto-responder "${name}" not found.`);
            await safeReply(interaction, { embeds: [embed], ephemeral: true });
            return;
        }

        const newStatus = !responder.enabled;
        
        try {
            await database.run('UPDATE auto_responders SET enabled = ? WHERE id = ?', [newStatus, responder.id]);
            
            // Update in memory
            responder.enabled = newStatus;
            
            const statusText = newStatus ? 'enabled' : 'disabled';
            const embed = EmbedHelper.success(
                'Auto-Responder Updated',
                `Auto-responder "${name}" has been ${statusText}.`
            );
            await safeReply(interaction, { embeds: [embed], ephemeral: true });

        } catch (error) {
            console.error('Error toggling auto-responder:', error);
            const embed = EmbedHelper.error('Update Failed', 'Failed to update auto-responder.');
            await safeReply(interaction, { embeds: [embed], ephemeral: true });
        }
    }

    async deleteAutoResponder(interaction) {
        const name = interaction.options.getString('name');
        const responder = Array.from(this.autoResponders.values()).find(r => 
            r.name.toLowerCase() === name.toLowerCase()
        );

        if (!responder) {
            const embed = EmbedHelper.error('Not Found', `Auto-responder "${name}" not found.`);
            await safeReply(interaction, { embeds: [embed], ephemeral: true });
            return;
        }

        try {
            await database.run('DELETE FROM auto_responders WHERE id = ?', [responder.id]);
            
            // Remove from memory
            this.autoResponders.delete(responder.id);
            
            const embed = EmbedHelper.success(
                'Auto-Responder Deleted',
                `Auto-responder "${name}" has been deleted.`
            );
            await safeReply(interaction, { embeds: [embed], ephemeral: true });

        } catch (error) {
            console.error('Error deleting auto-responder:', error);
            const embed = EmbedHelper.error('Delete Failed', 'Failed to delete auto-responder.');
            await safeReply(interaction, { embeds: [embed], ephemeral: true });
        }
    }

    async checkAutoResponders(message, ticketId) {
        if (!message.content) return false;

        const content = message.content.toLowerCase();
        
        for (const responder of this.autoResponders.values()) {
            if (!responder.enabled) continue;

            // Check if any trigger matches
            const hasMatch = responder.triggers.some(trigger => 
                content.includes(trigger.toLowerCase())
            );

            if (hasMatch) {
                // Check conditions
                if (await this.checkConditions(responder.conditions, message, ticketId)) {
                    // Send auto-response
                    await this.sendAutoResponse(message, responder.response);
                    return true;
                }
            }
        }

        return false;
    }

    async checkConditions(conditions, message, ticketId) {
        // Check new user condition
        if (conditions.new_user_only) {
            const userTickets = await database.get(
                'SELECT COUNT(*) as count FROM tickets WHERE user_id = ?',
                [message.author.id]
            );
            if (userTickets.count > 1) return false;
        }

        // Check business hours condition
        if (conditions.business_hours) {
            const now = new Date();
            const hour = now.getHours();
            const day = now.getDay(); // 0 = Sunday, 6 = Saturday
            
            // Business hours: Monday-Friday, 9 AM - 5 PM
            if (day === 0 || day === 6 || hour < 9 || hour >= 17) {
                return false;
            }
        }

        // Check ticket age condition
        if (conditions.max_ticket_age_minutes) {
            const ticket = await database.get('SELECT created_at FROM tickets WHERE id = ?', [ticketId]);
            if (ticket) {
                const ticketAge = (Date.now() - new Date(ticket.created_at).getTime()) / (1000 * 60);
                if (ticketAge > conditions.max_ticket_age_minutes) return false;
            }
        }

        return true;
    }

    async sendAutoResponse(message, response) {
        try {
            // Replace variables in response
            const formattedResponse = response
                .replace(/{username}/g, message.author.username)
                .replace(/{user}/g, message.author.toString())
                .replace(/{time}/g, new Date().toLocaleTimeString());

            const embed = new EmbedBuilder()
                .setDescription(formattedResponse)
                .setColor(0x00ff00)
                .setAuthor({ 
                    name: 'Auto-Response', 
                    iconURL: this.client.user.displayAvatarURL() 
                })
                .setTimestamp();

            await message.author.send({ embeds: [embed] });
            
            // Log the auto-response
            console.log(`📤 Auto-response sent to ${message.author.tag}`);

        } catch (error) {
            console.error('Error sending auto-response:', error);
        }
    }

    async setupWorkflowAutomation(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('⚙️ Workflow Automation Setup')
            .setDescription('Configure advanced automation workflows for your support system.')
            .setColor(0x9932cc)
            .addFields(
                {
                    name: '🔄 Available Automations',
                    value: '• Auto-responders for common questions\n• Ticket routing based on keywords\n• Escalation rules for urgent tickets\n• Follow-up reminders\n• SLA monitoring',
                    inline: false
                },
                {
                    name: '📋 Condition Types',
                    value: '• `new_user_only` - Only for first-time users\n• `business_hours` - Only during business hours\n• `max_ticket_age_minutes` - Based on ticket age\n• `user_role` - Based on user roles\n• `keyword_priority` - Priority based on keywords',
                    inline: false
                },
                {
                    name: '🎯 Response Variables',
                    value: '• `{username}` - User\'s name\n• `{user}` - User mention\n• `{time}` - Current time\n• `{ticket_id}` - Ticket number\n• `{category}` - Ticket category',
                    inline: false
                }
            )
            .setFooter({ text: 'Use /automation create to set up new auto-responders' });

        await safeReply(interaction, { embeds: [embed], ephemeral: true });
    }
}

// Slash command definitions
const commands = [
    new SlashCommandBuilder()
        .setName('automation')
        .setDescription('Manage automation and workflows')
        .addSubcommand(subcommand =>
            subcommand
                .setName('create')
                .setDescription('Create a new auto-responder')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('list')
                .setDescription('List all auto-responders')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('toggle')
                .setDescription('Enable or disable an auto-responder')
                .addStringOption(option =>
                    option.setName('name')
                        .setDescription('Name of the auto-responder')
                        .setRequired(true)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('delete')
                .setDescription('Delete an auto-responder')
                .addStringOption(option =>
                    option.setName('name')
                        .setDescription('Name of the auto-responder')
                        .setRequired(true)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('setup')
                .setDescription('Show automation setup guide')
        )
];

module.exports = {
    AutomationManager,
    commands
};

import discord
from discord.ext import commands
import asyncio
from datetime import datetime, timedelta
from typing import Optional, List

from database import db
from config import config
from utils.permissions import PermissionManager, can_manage_tickets
from utils.helpers import <PERSON><PERSON><PERSON><PERSON><PERSON>, MessageFormatter, safe_send, TimeParser

class TicketView(discord.ui.View):
    """Interactive view for ticket management"""
    
    def __init__(self, ticket_id: int):
        super().__init__(timeout=None)
        self.ticket_id = ticket_id
    
    @discord.ui.button(label='Close Ticket', style=discord.ButtonStyle.danger, emoji='🔒')
    async def close_ticket(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Close ticket button"""
        if not PermissionManager.can_manage_tickets(interaction.user):
            await interaction.response.send_message("❌ You don't have permission to close tickets.", ephemeral=True)
            return
        
        # Create modal for close reason
        modal = CloseTicketModal(self.ticket_id)
        await interaction.response.send_modal(modal)
    
    @discord.ui.button(label='Assign to Me', style=discord.ButtonStyle.secondary, emoji='👤')
    async def assign_ticket(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Assign ticket to staff member"""
        if not PermissionManager.can_manage_tickets(interaction.user):
            await interaction.response.send_message("❌ You don't have permission to assign tickets.", ephemeral=True)
            return
        
        # Update ticket assignment in database
        # This would require additional database methods
        await interaction.response.send_message(f"✅ Ticket assigned to {interaction.user.mention}", ephemeral=True)

class CloseTicketModal(discord.ui.Modal, title='Close Ticket'):
    """Modal for closing tickets with reason"""
    
    def __init__(self, ticket_id: int):
        super().__init__()
        self.ticket_id = ticket_id
    
    reason = discord.ui.TextInput(
        label='Reason (Optional)',
        placeholder='Enter reason for closing this ticket...',
        required=False,
        max_length=500
    )
    
    anonymous = discord.ui.TextInput(
        label='Anonymous Close? (yes/no)',
        placeholder='Type "yes" for anonymous close',
        required=False,
        max_length=3
    )
    
    async def on_submit(self, interaction: discord.Interaction):
        """Handle modal submission"""
        is_anonymous = self.anonymous.value.lower() in ['yes', 'y', 'true']
        reason = self.reason.value or "No reason provided"
        
        # Close the ticket
        success = await db.close_ticket(
            interaction.channel.id, 
            interaction.user.id, 
            reason, 
            is_anonymous
        )
        
        if success:
            # Get ticket info for user notification
            ticket = await db.get_ticket(interaction.channel.id)
            if ticket:
                user = interaction.guild.get_member(ticket['user_id'])
                if user:
                    # Send DM to user
                    if is_anonymous:
                        message = config.get('messages.anonymous_close_message')
                    else:
                        message = config.get('messages.close_message')
                    
                    embed = EmbedBuilder.info("Ticket Closed", message)
                    if reason != "No reason provided":
                        embed.add_field(name="Reason", value=reason, inline=False)
                    
                    await safe_send(user, embed=embed)
            
            # Send confirmation and schedule channel deletion
            embed = EmbedBuilder.success("Ticket Closed", f"Reason: {reason}")
            await interaction.response.send_message(embed=embed)
            
            # Delete channel after 10 seconds
            await asyncio.sleep(10)
            try:
                await interaction.channel.delete(reason=f"Ticket closed by {interaction.user}")
            except discord.NotFound:
                pass
        else:
            await interaction.response.send_message("❌ Failed to close ticket.", ephemeral=True)

class ModMail(commands.Cog):
    """Core ModMail functionality"""
    
    def __init__(self, bot):
        self.bot = bot
        self.active_tickets = {}  # Cache for active tickets
    
    @commands.Cog.listener()
    async def on_message(self, message):
        """Handle incoming messages for modmail"""
        # Ignore bot messages
        if message.author.bot:
            return
        
        # Handle DMs to bot
        if isinstance(message.channel, discord.DMChannel):
            await self._handle_dm(message)
        
        # Handle messages in ticket channels
        elif message.channel.category_id == config.get('modmail.category_id'):
            await self._handle_ticket_message(message)
    
    async def _handle_dm(self, message):
        """Handle direct messages to the bot"""
        user = message.author
        
        # Check if user is blacklisted
        if await db.is_blacklisted(user.id):
            embed = EmbedBuilder.error("Blacklisted", config.get('messages.blacklist_message'))
            await safe_send(user, embed=embed)
            return
        
        # Check if user has too many open tickets
        open_tickets = await db.get_user_tickets(user.id, "open")
        max_tickets = config.get('modmail.max_tickets_per_user', 3)
        
        if len(open_tickets) >= max_tickets:
            message_text = MessageFormatter.format_message(
                config.get('messages.max_tickets_message'),
                max_tickets=max_tickets
            )
            embed = EmbedBuilder.warning("Too Many Tickets", message_text)
            await safe_send(user, embed=embed)
            return
        
        # Check if user already has an open ticket
        existing_ticket = None
        for ticket in open_tickets:
            if ticket['status'] == 'open':
                existing_ticket = ticket
                break
        
        if existing_ticket:
            # Forward message to existing ticket
            channel = self.bot.get_channel(existing_ticket['channel_id'])
            if channel:
                await self._forward_user_message(message, channel, existing_ticket['id'])
                await db.update_ticket_activity(channel.id)
        else:
            # Create new ticket
            await self._create_ticket(user, message)
    
    async def _create_ticket(self, user: discord.User, initial_message: discord.Message):
        """Create a new support ticket"""
        guild = self.bot.get_guild(config.guild_id)
        if not guild:
            return
        
        category = guild.get_channel(config.get('modmail.category_id'))
        if not category:
            embed = EmbedBuilder.error("Configuration Error", "ModMail category not found.")
            await safe_send(user, embed=embed)
            return
        
        # Create ticket channel
        channel_name = f"ticket-{user.name}-{user.discriminator}"
        
        try:
            channel = await category.create_text_channel(
                name=channel_name,
                topic=f"Support ticket for {user} ({user.id})"
            )
            
            # Create ticket in database
            ticket_id = await db.create_ticket(user.id, channel.id, guild.id)
            
            # Send welcome message to user
            welcome_msg = MessageFormatter.format_message(
                config.get('messages.welcome_message'),
                username=user.name,
                user=user.mention
            )
            embed = EmbedBuilder.success("Ticket Created", welcome_msg)
            await safe_send(user, embed=embed)
            
            # Set up ticket channel
            await self._setup_ticket_channel(channel, user, ticket_id, initial_message)
            
            # Log ticket creation
            log_channel = guild.get_channel(config.get('modmail.log_channel_id'))
            if log_channel:
                log_embed = EmbedBuilder.ticket_created(user, ticket_id)
                await safe_send(log_channel, embed=log_embed)
            
        except discord.Forbidden:
            embed = EmbedBuilder.error("Permission Error", "Bot lacks permission to create channels.")
            await safe_send(user, embed=embed)
    
    async def _setup_ticket_channel(self, channel: discord.TextChannel, user: discord.User, 
                                  ticket_id: int, initial_message: discord.Message):
        """Set up the ticket channel with initial content"""
        # Create ticket info embed
        embed = EmbedBuilder.ticket_created(user, ticket_id)
        embed.add_field(name="Initial Message", value=initial_message.content[:1000], inline=False)
        
        # Add ticket management view
        view = TicketView(ticket_id)
        
        await channel.send(embed=embed, view=view)
        
        # Forward the initial message
        await self._forward_user_message(initial_message, channel, ticket_id)
        
        # Ping staff role if configured
        staff_role_id = config.get('permissions.staff_role_id')
        if staff_role_id:
            staff_role = channel.guild.get_role(staff_role_id)
            if staff_role:
                await channel.send(f"{staff_role.mention} New ticket created!")
    
    async def _forward_user_message(self, message: discord.Message, 
                                  channel: discord.TextChannel, ticket_id: int):
        """Forward user message to ticket channel"""
        # Create embed for user message
        embed = discord.Embed(
            description=message.content,
            color=discord.Color.blue(),
            timestamp=message.created_at
        )
        embed.set_author(name=f"{message.author}", icon_url=message.author.display_avatar.url)
        
        # Handle attachments
        files = []
        if message.attachments:
            attachment_text = "\n".join([f"[{att.filename}]({att.url})" for att in message.attachments])
            embed.add_field(name="Attachments", value=attachment_text, inline=False)
        
        await channel.send(embed=embed)
        
        # Log message to database
        await db.log_message(
            ticket_id, message.id, message.author.id, 
            message.content, [att.url for att in message.attachments]
        )
    
    async def _handle_ticket_message(self, message):
        """Handle messages in ticket channels"""
        # Get ticket info
        ticket = await db.get_ticket(message.channel.id)
        if not ticket:
            return
        
        # Skip if message is from the ticket user (already handled in DM)
        if message.author.id == ticket['user_id']:
            return
        
        # Check if sender is staff
        if not PermissionManager.can_manage_tickets(message.author):
            return
        
        # Forward staff message to user
        user = self.bot.get_user(ticket['user_id'])
        if user:
            await self._forward_staff_message(message, user, ticket['id'])
            await db.update_ticket_activity(message.channel.id)
    
    async def _forward_staff_message(self, message: discord.Message, 
                                   user: discord.User, ticket_id: int):
        """Forward staff message to user"""
        # Create embed for staff message
        embed = discord.Embed(
            description=message.content,
            color=discord.Color.green(),
            timestamp=message.created_at
        )
        embed.set_author(name="Support Team", icon_url=message.guild.icon.url if message.guild.icon else None)
        
        # Handle attachments
        if message.attachments:
            attachment_text = "\n".join([f"[{att.filename}]({att.url})" for att in message.attachments])
            embed.add_field(name="Attachments", value=attachment_text, inline=False)
        
        await safe_send(user, embed=embed)
        
        # Log message to database
        await db.log_message(
            ticket_id, message.id, message.author.id, 
            message.content, [att.url for att in message.attachments], 
            is_staff=True
        )
    
    @commands.command(name='close')
    @can_manage_tickets()
    async def close_ticket_command(self, ctx, *, reason: str = "No reason provided"):
        """Close the current ticket"""
        ticket = await db.get_ticket(ctx.channel.id)
        if not ticket:
            embed = EmbedBuilder.error("Not a Ticket", "This command can only be used in ticket channels.")
            await ctx.send(embed=embed)
            return
        
        # Close the ticket
        success = await db.close_ticket(ctx.channel.id, ctx.author.id, reason)
        
        if success:
            # Notify user
            user = self.bot.get_user(ticket['user_id'])
            if user:
                embed = EmbedBuilder.info("Ticket Closed", config.get('messages.close_message'))
                embed.add_field(name="Reason", value=reason, inline=False)
                await safe_send(user, embed=embed)
            
            # Send confirmation
            embed = EmbedBuilder.success("Ticket Closed", f"Reason: {reason}")
            await ctx.send(embed=embed)
            
            # Delete channel after delay
            await asyncio.sleep(10)
            try:
                await ctx.channel.delete(reason=f"Ticket closed by {ctx.author}")
            except discord.NotFound:
                pass

async def setup(bot):
    await bot.add_cog(ModMail(bot))

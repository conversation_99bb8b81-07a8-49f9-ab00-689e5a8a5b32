# 🤖 ChatterBot - Advanced ModMail Bot

ChatterBot is a highly advanced Discord ModMail bot with AI-powered features, comprehensive ticket management, and enterprise-level functionality.

## ✨ Features

### 🎫 Core ModMail Features
- **Automatic Ticket Creation** - Users DM the bot to create support tickets
- **Category-based Organization** - Organize tickets by categories
- **Staff Role Management** - Multiple permission levels for different staff roles
- **Real-time Message Forwarding** - Seamless communication between users and staff

### 🔧 Advanced Management
- **Anonymous Replies & Closing** - Staff can respond anonymously
- **Blacklisting/Whitelisting** - Prevent or allow specific users from creating tickets
- **Bulk Operations** - Close multiple tickets at once
- **Scheduled Closing** - Auto-close tickets after inactivity or set time
- **Ticket Assignment** - Assign tickets to specific staff members

### 📝 Snippet System
- **Canned Responses** - Save frequently used responses
- **Category Organization** - Organize snippets by categories
- **Usage Analytics** - Track snippet usage statistics
- **Quick Access** - Use snippets with simple commands

### 🤖 AI-Powered Features
- **AI Reply Generation** - Generate contextual responses using OpenAI
- **Conversation Summarization** - AI-powered ticket summaries
- **Multi-language Translation** - Translate messages in real-time
- **Smart Context Understanding** - AI analyzes conversation history

### 📊 Analytics & Logging
- **Comprehensive Logging** - Log all messages and staff actions
- **Transcript Generation** - Create detailed ticket transcripts
- **Statistics Dashboard** - Track bot performance and usage
- **Advanced Reporting** - Generate detailed reports

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- Discord Bot Token
- OpenAI API Key (optional, for AI features)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd chatterbot
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Run the bot**
   ```bash
   python main.py
   ```

### Configuration

Edit your `.env` file with the following required settings:

```env
# Discord Configuration
DISCORD_TOKEN=your_discord_bot_token_here
GUILD_ID=your_server_id_here

# Channel Configuration
MODMAIL_CATEGORY_ID=your_modmail_category_id_here
LOG_CHANNEL_ID=your_log_channel_id_here

# Role Configuration
STAFF_ROLE_ID=your_staff_role_id_here
ADMIN_ROLE_ID=your_admin_role_id_here

# AI Features (Optional)
OPENAI_API_KEY=your_openai_api_key_here
ENABLE_AI_REPLIES=true
```

## 📚 Commands

### 🎫 Ticket Management
- `!close [reason]` - Close the current ticket
- `!aclose [reason]` - Close ticket anonymously
- `!areply <message>` - Send anonymous reply to user
- `!schedule <time> [reason]` - Schedule ticket to close after specified time

### 📝 Snippets
- `!snippet <name>` - Use a snippet
- `!snippet list [category]` - List all snippets
- `!snippet create` - Create a new snippet
- `!snippet delete <name>` - Delete a snippet
- `!snippet info <name>` - Show snippet information
- `!snippet search <query>` - Search snippets

### 🤖 AI Features
- `!aireply [prompt]` - Generate AI-powered response
- `!summarize` - Create AI summary of ticket conversation
- `!translate <language> <text>` - Translate text using AI

### ⚙️ Administration
- `!blacklist add <user> [reason]` - Blacklist a user
- `!blacklist remove <user>` - Remove user from blacklist
- `!blacklist list` - List blacklisted users
- `!blacklist check <user>` - Check if user is blacklisted
- `!closeall [reason]` - Close all open tickets
- `!stats` - Show bot statistics

## 🔧 Advanced Configuration

### Ticket Categories
Create custom ticket categories with automatic responses:

```json
{
  "categories": {
    "billing": {
      "name": "Billing Support",
      "emoji": "💳",
      "ping_role": "billing_team",
      "auto_response": "Thank you for contacting billing support..."
    }
  }
}
```

### Custom Messages
Customize all bot messages in the configuration:

```json
{
  "messages": {
    "welcome_message": "Hello {username}! Thanks for contacting support.",
    "close_message": "Your ticket has been closed.",
    "blacklist_message": "You are blacklisted from creating tickets."
  }
}
```

### AI Configuration
Fine-tune AI behavior:

```json
{
  "ai_features": {
    "model": "gpt-3.5-turbo",
    "max_tokens": 500,
    "temperature": 0.7
  }
}
```

## 🛡️ Permissions

ChatterBot uses a role-based permission system:

- **Admin Role** - Full access to all commands and settings
- **Moderator Role** - Can manage tickets, blacklist users, use AI features
- **Staff Role** - Can reply to tickets, use snippets
- **Support Role** - Basic ticket access

## 📊 Database Schema

ChatterBot uses SQLite for data storage with the following tables:

- `tickets` - Ticket information and status
- `messages` - Message logs for transcripts
- `snippets` - Saved response templates
- `users` - User settings and blacklist
- `staff_actions` - Staff action logs
- `scheduled_tasks` - Scheduled operations

## 🔌 API Integration

### OpenAI Integration
ChatterBot integrates with OpenAI for:
- Contextual response generation
- Conversation summarization
- Multi-language translation
- Sentiment analysis

### Webhook Support
Configure webhooks for external integrations:
- Ticket creation notifications
- Staff action logs
- Performance metrics

## 🚀 Deployment

### Docker Deployment
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["python", "main.py"]
```

### Systemd Service
```ini
[Unit]
Description=ChatterBot ModMail
After=network.target

[Service]
Type=simple
User=chatterbot
WorkingDirectory=/opt/chatterbot
ExecStart=/usr/bin/python3 main.py
Restart=always

[Install]
WantedBy=multi-user.target
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- **Documentation**: [Wiki](link-to-wiki)
- **Discord Server**: [Join our support server](discord-invite-link)
- **Issues**: [GitHub Issues](github-issues-link)

## 🙏 Acknowledgments

- Discord.py community for the excellent library
- OpenAI for AI capabilities
- All contributors and users of ChatterBot

---

**ChatterBot** - Making customer support effortless with AI-powered automation.

# 🤖 ChatterBot - Advanced ModMail Bot (JavaScript Edition)

ChatterBot is a highly advanced Discord ModMail bot built with Node.js, featuring AI-powered responses using Puter.js, comprehensive ticket management, and enterprise-level functionality.

## ✨ Features

### 🎫 Core ModMail Features
- **Automatic Ticket Creation** - Users DM the bot to create support tickets
- **Category-based Organization** - Organize tickets by categories
- **Staff Role Management** - Multiple permission levels for different staff roles
- **Real-time Message Forwarding** - Seamless communication between users and staff

### 🔧 Advanced Management
- **Anonymous Replies & Closing** - Staff can respond anonymously using `/areply` and `/aclose`
- **Blacklisting/Whitelisting** - Prevent or allow specific users from creating tickets
- **Bulk Operations** - Close multiple tickets at once with `/closeall`
- **Scheduled Closing** - Auto-close tickets after inactivity or set time with `/schedule`
- **Ticket Assignment** - Assign tickets to specific staff members

### 📝 Snippet System
- **Canned Responses** - Save frequently used responses with `/snippets create`
- **Category Organization** - Organize snippets by categories
- **Usage Analytics** - Track snippet usage statistics
- **Quick Access** - Use snippets with `/snippet <name>`

### 🤖 AI-Powered Features (Puter.js)
- **AI Reply Generation** - Generate contextual responses using `/aireply`
- **Conversation Summarization** - AI-powered ticket summaries with `/summarize`
- **Multi-language Translation** - Translate messages in real-time with `/translate`
- **Smart Context Understanding** - AI analyzes conversation history

### 📊 Analytics & Logging
- **Comprehensive Logging** - Log all messages and staff actions
- **Statistics Dashboard** - Track bot performance with `/stats`
- **Advanced Reporting** - Generate detailed reports

## 🚀 Quick Start

### Prerequisites
- Node.js 16 or higher
- Discord Bot Token
- Puter.js API Key (optional, for AI features)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd chatterbot-js
   ```

2. **Run the setup script**
   ```bash
   npm run setup
   ```

3. **Configure environment**
   ```bash
   # Edit .env with your configuration
   nano .env
   ```

4. **Start the bot**
   ```bash
   npm start
   ```

### Configuration

Edit your `.env` file with the following settings:

```env
# Discord Configuration
DISCORD_TOKEN=your_discord_bot_token_here
GUILD_ID=your_server_id_here

# Puter.js AI Configuration
PUTER_API_KEY=your_puter_api_key_here
PUTER_APP_ID=your_puter_app_id_here

# Channel Configuration
MODMAIL_CATEGORY_ID=your_modmail_category_id_here
LOG_CHANNEL_ID=your_log_channel_id_here

# Role Configuration
STAFF_ROLE_ID=your_staff_role_id_here
ADMIN_ROLE_ID=your_admin_role_id_here

# AI Features
ENABLE_AI_REPLIES=true
```

## 📚 Commands

### 🎫 Ticket Management
- `/close [reason]` - Close the current ticket
- `/aclose [reason]` - Close ticket anonymously
- `/areply <message>` - Send anonymous reply to user
- `/schedule <time> [reason]` - Schedule ticket to close after specified time

### 📝 Snippets
- `/snippet <name>` - Use a snippet
- `/snippets list [category]` - List all snippets
- `/snippets create` - Create a new snippet
- `/snippets delete <name>` - Delete a snippet
- `/snippets info <name>` - Show snippet information
- `/snippets search <query>` - Search snippets

### 🤖 AI Features (Puter.js)
- `/aireply [prompt]` - Generate AI-powered response
- `/summarize` - Create AI summary of ticket conversation
- `/translate <language> <text>` - Translate text using AI

### ⚙️ Administration
- `/blacklist add <user> [reason]` - Blacklist a user
- `/blacklist remove <user>` - Remove user from blacklist
- `/blacklist list` - List blacklisted users
- `/blacklist check <user>` - Check if user is blacklisted
- `/closeall [reason]` - Close all open tickets
- `/stats` - Show bot statistics

## 🔧 Advanced Configuration

### Puter.js AI Setup

1. **Get API Key**
   - Visit [Puter.com](https://puter.com)
   - Create an account and get your API key
   - Add to `.env` file

2. **Configure AI Settings**
   ```javascript
   // In config/config.js
   ai_features: {
       enabled: true,
       puter_api_key: process.env.PUTER_API_KEY,
       puter_app_id: process.env.PUTER_APP_ID,
       max_tokens: 500,
       temperature: 0.7
   }
   ```

### Custom Messages
Customize all bot messages in the configuration:

```javascript
messages: {
    welcome_message: 'Hello {username}! Thanks for contacting support.',
    close_message: 'Your ticket has been closed.',
    blacklist_message: 'You are blacklisted from creating tickets.'
}
```

### Ticket Categories
Create custom ticket categories:

```javascript
// Add to database or configuration
{
    name: 'billing',
    description: 'Billing Support',
    emoji: '💳',
    ping_role_id: 'billing_team_role_id',
    auto_response: 'Thank you for contacting billing support...'
}
```

## 🛡️ Permissions

ChatterBot uses a role-based permission system:

- **Admin Role** - Full access to all commands and settings
- **Moderator Role** - Can manage tickets, blacklist users, use AI features
- **Staff Role** - Can reply to tickets, use snippets
- **Support Role** - Basic ticket access

## 📊 Database Schema

ChatterBot uses SQLite for data storage with the following tables:

- `tickets` - Ticket information and status
- `messages` - Message logs for transcripts
- `snippets` - Saved response templates
- `users` - User settings and blacklist
- `staff_actions` - Staff action logs
- `scheduled_tasks` - Scheduled operations

## 🚀 Deployment

### Development
```bash
npm run dev  # Uses nodemon for auto-restart
```

### Production with PM2
```bash
npm install -g pm2
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### Docker Deployment
```bash
docker-compose up -d
```

### Systemd Service
```bash
sudo cp chatterbot.service /etc/systemd/system/
sudo systemctl enable chatterbot
sudo systemctl start chatterbot
```

## 🔌 Puter.js Integration

ChatterBot leverages Puter.js for advanced AI capabilities:

```javascript
// Example AI integration
const response = await this.puter.ai.chat({
    messages: conversationHistory,
    model: 'gpt-3.5-turbo',
    max_tokens: 500,
    temperature: 0.7
});
```

### AI Features Include:
- **Contextual Responses** - AI understands conversation history
- **Smart Summarization** - Automatic ticket summaries
- **Multi-language Support** - Real-time translation
- **Sentiment Analysis** - Understand user emotions

## 📁 Project Structure

```
chatterbot-js/
├── index.js                 # Main bot entry point
├── package.json            # Dependencies and scripts
├── setup.js               # Automated setup script
├── config/
│   └── config.js          # Configuration management
├── database/
│   └── database.js        # SQLite database operations
├── commands/
│   ├── modmail.js         # Core modmail functionality
│   ├── admin.js           # Admin commands
│   ├── snippets.js        # Snippet management
│   └── ai.js              # Puter.js AI features
├── utils/
│   ├── permissions.js     # Permission handling
│   ├── helpers.js         # Helper functions
│   └── scheduler.js       # Task scheduling
└── data/
    └── database.db        # SQLite database (auto-created)
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- **Documentation**: [Wiki](link-to-wiki)
- **Discord Server**: [Join our support server](discord-invite-link)
- **Issues**: [GitHub Issues](github-issues-link)

## 🙏 Acknowledgments

- Discord.js community for the excellent library
- Puter.js team for AI capabilities
- All contributors and users of ChatterBot

---

**ChatterBot** - Making customer support effortless with AI-powered automation using Puter.js.

# 🤖 ChatterBot - Advanced ModMail Bot (JavaScript Edition)

ChatterBot is a highly advanced Discord ModMail bot built with Node.js, featuring AI-powered responses using OpenRouter's free models, comprehensive ticket management, and enterprise-level functionality.

## ✨ Features

### 🎫 Core ModMail Features
- **Automatic Ticket Creation** - Users DM the bot to create support tickets
- **Category-based Organization** - Organize tickets by categories
- **Staff Role Management** - Multiple permission levels for different staff roles
- **Real-time Message Forwarding** - Seamless communication between users and staff

### 🔧 Advanced Management
- **Anonymous Replies & Closing** - Staff can respond anonymously using `/areply` and `/aclose`
- **Blacklisting/Whitelisting** - Prevent or allow specific users from creating tickets
- **Bulk Operations** - Close multiple tickets at once with `/closeall`
- **Scheduled Closing** - Auto-close tickets after inactivity or set time with `/schedule`
- **Ticket Assignment** - Assign tickets to specific staff members

### 📝 Snippet System
- **Canned Responses** - Save frequently used responses with `/snippets create`
- **Category Organization** - Organize snippets by categories
- **Usage Analytics** - Track snippet usage statistics
- **Quick Access** - Use snippets with `/snippet <name>`

### 🤖 AI-Powered Features (OpenRouter)
- **AI Reply Generation** - Generate contextual responses using `/aireply`
- **Conversation Summarization** - AI-powered ticket summaries with `/summarize`
- **Multi-language Translation** - Translate messages in real-time with `/translate`
- **Smart Context Understanding** - AI analyzes conversation history
- **Free AI Models** - Uses OpenRouter's free models like Llama 3.2

### 🔄 Smart Automation
- **Auto-Responders** - Intelligent keyword-based responses with `/automation create`
- **Conditional Logic** - Complex rules based on user type, time, ticket age
- **Workflow Automation** - Ticket routing and escalation rules
- **Business Hours** - Time-based response handling
- **A/B Testing** - Optimize response effectiveness

### 🎫 Interactive Ticket System
- **Ticket Panels** - Beautiful category selection with `/ticket panel`
- **Priority Levels** - Urgent, High, Normal, Low priority system
- **Staff Assignment** - Claim and transfer tickets between categories
- **Rich Embeds** - Professional ticket formatting with user info
- **Category-specific** - Auto-responses and role pings per category

### 📊 Advanced Analytics & Reporting
- **Comprehensive Analytics** - Detailed performance metrics with `/analytics overview`
- **Visual Heatmaps** - Peak activity analysis with `/analytics heatmap`
- **CSV Export** - Data portability for external analysis
- **Real-time Dashboards** - Live performance monitoring
- **SLA Tracking** - Response time and resolution metrics
- **Staff Performance** - Individual and team statistics

## 🚀 Quick Start

### Prerequisites
- Node.js 16 or higher
- Discord Bot Token
- OpenRouter API Key (optional, for AI features)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd chatterbot-js
   ```

2. **Run the setup script**
   ```bash
   npm run setup
   ```

3. **Configure environment**
   ```bash
   # Edit .env with your configuration
   nano .env
   ```

4. **Start the bot**
   ```bash
   npm start
   ```

### Configuration

Edit your `.env` file with the following settings:

```env
# Discord Configuration
DISCORD_TOKEN=your_discord_bot_token_here
GUILD_ID=your_server_id_here

# OpenRouter AI Configuration
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Channel Configuration
MODMAIL_CATEGORY_ID=your_modmail_category_id_here
LOG_CHANNEL_ID=your_log_channel_id_here

# Role Configuration
STAFF_ROLE_ID=your_staff_role_id_here
ADMIN_ROLE_ID=your_admin_role_id_here

# AI Features
ENABLE_AI_REPLIES=true
```

## 📚 Commands

### 🎫 Ticket Management
- `/close [reason]` - Close the current ticket
- `/aclose [reason]` - Close ticket anonymously
- `/areply <message>` - Send anonymous reply to user
- `/schedule <time> [reason]` - Schedule ticket to close after specified time

### 📝 Snippets
- `/snippet <name>` - Use a snippet
- `/snippets list [category]` - List all snippets
- `/snippets create` - Create a new snippet
- `/snippets delete <name>` - Delete a snippet
- `/snippets info <name>` - Show snippet information
- `/snippets search <query>` - Search snippets

### 🤖 AI Features (OpenRouter)
- `/aireply [prompt]` - Generate AI-powered response
- `/summarize` - Create AI summary of ticket conversation
- `/translate <language> <text>` - Translate text using AI

### ⚙️ Administration
- `/blacklist add <user> [reason]` - Blacklist a user
- `/blacklist remove <user>` - Remove user from blacklist
- `/blacklist list` - List blacklisted users
- `/blacklist check <user>` - Check if user is blacklisted
- `/closeall [reason]` - Close all open tickets
- `/stats` - Show bot statistics

### 📊 Analytics & Reporting
- `/analytics overview` - Comprehensive analytics with CSV export
- `/analytics heatmap` - Visual ticket creation heatmap
- `/ticket list [user] [status]` - List user tickets
- `/ticket transfer <category>` - Transfer ticket to different category

### 🔄 Automation & Workflows
- `/automation create` - Create new auto-responder
- `/automation list` - List all auto-responders
- `/automation toggle <name>` - Enable/disable auto-responder
- `/automation delete <name>` - Delete auto-responder
- `/automation setup` - Show automation guide

### 🎫 Interactive Tickets
- `/ticket panel` - Create interactive ticket creation panel
- `/ticket list [user]` - List tickets for user
- `/ticket transfer <category>` - Move ticket to different category

### 🎯 Showcase & Help
- `/showcase features` - Interactive feature showcase
- `/showcase quickstart` - Quick start guide

## 🔧 Advanced Configuration

### OpenRouter AI Setup

1. **Get API Key**
   - Visit [OpenRouter.ai](https://openrouter.ai)
   - Create an account and get your API key
   - Add to `.env` file

2. **Configure AI Settings**
   ```javascript
   // In config/config.js
   ai_features: {
       enabled: true,
       openrouter_api_key: process.env.OPENROUTER_API_KEY,
       model: 'meta-llama/llama-3.2-3b-instruct:free',
       max_tokens: 500,
       temperature: 0.7
   }
   ```

### Custom Messages
Customize all bot messages in the configuration:

```javascript
messages: {
    welcome_message: 'Hello {username}! Thanks for contacting support.',
    close_message: 'Your ticket has been closed.',
    blacklist_message: 'You are blacklisted from creating tickets.'
}
```

### Ticket Categories
Create custom ticket categories:

```javascript
// Add to database or configuration
{
    name: 'billing',
    description: 'Billing Support',
    emoji: '💳',
    ping_role_id: 'billing_team_role_id',
    auto_response: 'Thank you for contacting billing support...'
}
```

## 🛡️ Permissions

ChatterBot uses a role-based permission system:

- **Admin Role** - Full access to all commands and settings
- **Moderator Role** - Can manage tickets, blacklist users, use AI features
- **Staff Role** - Can reply to tickets, use snippets
- **Support Role** - Basic ticket access

## 📊 Database Schema

ChatterBot uses SQLite for data storage with the following tables:

- `tickets` - Ticket information and status
- `messages` - Message logs for transcripts
- `snippets` - Saved response templates
- `users` - User settings and blacklist
- `staff_actions` - Staff action logs
- `scheduled_tasks` - Scheduled operations

## 🚀 Deployment

### Development
```bash
npm run dev  # Uses nodemon for auto-restart
```

### Production with PM2
```bash
npm install -g pm2
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### Docker Deployment
```bash
docker-compose up -d
```

### Systemd Service
```bash
sudo cp chatterbot.service /etc/systemd/system/
sudo systemctl enable chatterbot
sudo systemctl start chatterbot
```

## 🔌 OpenRouter Integration

ChatterBot leverages OpenRouter for advanced AI capabilities using free models:

```javascript
// Example AI integration
const response = await this.openai.chat.completions.create({
    messages: conversationHistory,
    model: 'meta-llama/llama-3.2-3b-instruct:free',
    max_tokens: 500,
    temperature: 0.7
});
```

### AI Features Include:
- **Free AI Models** - Uses OpenRouter's free tier models
- **Contextual Responses** - AI understands conversation history
- **Smart Summarization** - Automatic ticket summaries
- **Multi-language Support** - Real-time translation

## 📁 Project Structure

```
chatterbot-js/
├── index.js                 # Main bot entry point
├── package.json            # Dependencies and scripts
├── setup.js               # Automated setup script
├── config/
│   └── config.js          # Configuration management
├── database/
│   └── database.js        # SQLite database operations
├── commands/
│   ├── modmail.js         # Core modmail functionality
│   ├── admin.js           # Admin commands
│   ├── snippets.js        # Snippet management
│   └── ai.js              # Puter.js AI features
├── utils/
│   ├── permissions.js     # Permission handling
│   ├── helpers.js         # Helper functions
│   └── scheduler.js       # Task scheduling
└── data/
    └── database.db        # SQLite database (auto-created)
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- **Documentation**: [Wiki](link-to-wiki)
- **Discord Server**: [Join our support server](discord-invite-link)
- **Issues**: [GitHub Issues](github-issues-link)

## 🙏 Acknowledgments

- Discord.js community for the excellent library
- OpenRouter team for free AI model access
- All contributors and users of ChatterBot

---

**ChatterBot** - Making customer support effortless with AI-powered automation using OpenRouter's free models.

const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, AttachmentBuilder } = require('discord.js');
const database = require('../database/database');
const { EmbedHelper, safeReply } = require('../utils/helpers');
const { requireAdmin } = require('../utils/permissions');
const fs = require('fs-extra');
const path = require('path');

class AnalyticsManager {
    constructor(client) {
        this.client = client;
    }

    async generateAdvancedStats(interaction) {
        await interaction.deferReply({ ephemeral: true });

        try {
            // Get comprehensive statistics
            const stats = await this.getComprehensiveStats();
            
            // Create multiple embeds for different categories
            const embeds = [
                this.createOverviewEmbed(stats),
                this.createTicketStatsEmbed(stats),
                this.createUserStatsEmbed(stats),
                this.createPerformanceEmbed(stats)
            ];

            // Generate CSV report
            const csvData = await this.generateCSVReport(stats);
            const attachment = new AttachmentBuilder(Buffer.from(csvData), { name: 'chatterbot-analytics.csv' });

            await interaction.editReply({ 
                embeds: embeds,
                files: [attachment]
            });

        } catch (error) {
            console.error('Error generating analytics:', error);
            const embed = EmbedHelper.error('Analytics Error', 'Failed to generate analytics report.');
            await interaction.editReply({ embeds: [embed] });
        }
    }

    async getComprehensiveStats() {
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const thisWeek = new Date(today.getTime() - (7 * 24 * 60 * 60 * 1000));
        const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1);

        const stats = {};

        // Basic counts
        stats.totalTickets = await database.get('SELECT COUNT(*) as count FROM tickets');
        stats.openTickets = await database.get('SELECT COUNT(*) as count FROM tickets WHERE status = "open"');
        stats.closedTickets = await database.get('SELECT COUNT(*) as count FROM tickets WHERE status = "closed"');
        
        // Time-based stats
        stats.ticketsToday = await database.get(`
            SELECT COUNT(*) as count FROM tickets 
            WHERE DATE(created_at) = DATE('now')
        `);
        
        stats.ticketsThisWeek = await database.get(`
            SELECT COUNT(*) as count FROM tickets 
            WHERE created_at >= ?
        `, [thisWeek.toISOString()]);
        
        stats.ticketsThisMonth = await database.get(`
            SELECT COUNT(*) as count FROM tickets 
            WHERE created_at >= ?
        `, [thisMonth.toISOString()]);

        // Resolution stats
        stats.avgResolutionTime = await database.get(`
            SELECT AVG(
                (julianday(closed_at) - julianday(created_at)) * 24 * 60
            ) as avg_minutes
            FROM tickets 
            WHERE status = 'closed' AND closed_at IS NOT NULL
        `);

        // User stats
        stats.totalUsers = await database.get('SELECT COUNT(DISTINCT user_id) as count FROM tickets');
        stats.blacklistedUsers = await database.get('SELECT COUNT(*) as count FROM users WHERE is_blacklisted = TRUE');
        
        // Staff stats
        stats.totalSnippets = await database.get('SELECT COUNT(*) as count FROM snippets');
        stats.snippetUsage = await database.get('SELECT SUM(usage_count) as total FROM snippets');
        
        // Category breakdown
        stats.categoryBreakdown = await database.all(`
            SELECT category, COUNT(*) as count 
            FROM tickets 
            GROUP BY category 
            ORDER BY count DESC
        `);

        // Daily ticket trends (last 7 days)
        stats.dailyTrends = await database.all(`
            SELECT DATE(created_at) as date, COUNT(*) as count
            FROM tickets 
            WHERE created_at >= ?
            GROUP BY DATE(created_at)
            ORDER BY date DESC
            LIMIT 7
        `, [thisWeek.toISOString()]);

        // Top staff members by tickets closed
        stats.topStaff = await database.all(`
            SELECT closed_by, COUNT(*) as tickets_closed
            FROM tickets 
            WHERE status = 'closed' AND closed_by IS NOT NULL
            GROUP BY closed_by
            ORDER BY tickets_closed DESC
            LIMIT 5
        `);

        return stats;
    }

    createOverviewEmbed(stats) {
        const embed = new EmbedBuilder()
            .setTitle('📊 ChatterBot Analytics Overview')
            .setColor(0x0099ff)
            .setTimestamp()
            .addFields(
                { name: '🎫 Total Tickets', value: stats.totalTickets.count.toString(), inline: true },
                { name: '🟢 Open Tickets', value: stats.openTickets.count.toString(), inline: true },
                { name: '🔴 Closed Tickets', value: stats.closedTickets.count.toString(), inline: true },
                { name: '📅 Today', value: stats.ticketsToday.count.toString(), inline: true },
                { name: '📅 This Week', value: stats.ticketsThisWeek.count.toString(), inline: true },
                { name: '📅 This Month', value: stats.ticketsThisMonth.count.toString(), inline: true }
            );

        if (stats.avgResolutionTime.avg_minutes) {
            const hours = Math.floor(stats.avgResolutionTime.avg_minutes / 60);
            const minutes = Math.floor(stats.avgResolutionTime.avg_minutes % 60);
            embed.addFields({ 
                name: '⏱️ Avg Resolution Time', 
                value: `${hours}h ${minutes}m`, 
                inline: true 
            });
        }

        return embed;
    }

    createTicketStatsEmbed(stats) {
        const embed = new EmbedBuilder()
            .setTitle('🎫 Ticket Statistics')
            .setColor(0x00ff00)
            .setTimestamp();

        // Category breakdown
        if (stats.categoryBreakdown.length > 0) {
            const categoryText = stats.categoryBreakdown
                .map(cat => `**${cat.category}**: ${cat.count}`)
                .join('\n');
            embed.addFields({ name: '📂 Categories', value: categoryText, inline: false });
        }

        // Daily trends
        if (stats.dailyTrends.length > 0) {
            const trendsText = stats.dailyTrends
                .map(day => `**${day.date}**: ${day.count} tickets`)
                .join('\n');
            embed.addFields({ name: '📈 Daily Trends (Last 7 Days)', value: trendsText, inline: false });
        }

        return embed;
    }

    createUserStatsEmbed(stats) {
        const embed = new EmbedBuilder()
            .setTitle('👥 User Statistics')
            .setColor(0xff9900)
            .setTimestamp()
            .addFields(
                { name: '👤 Total Users', value: stats.totalUsers.count.toString(), inline: true },
                { name: '🚫 Blacklisted', value: stats.blacklistedUsers.count.toString(), inline: true },
                { name: '📝 Total Snippets', value: stats.totalSnippets.count.toString(), inline: true },
                { name: '🔄 Snippet Usage', value: (stats.snippetUsage.total || 0).toString(), inline: true }
            );

        return embed;
    }

    createPerformanceEmbed(stats) {
        const embed = new EmbedBuilder()
            .setTitle('⚡ Performance Metrics')
            .setColor(0x9932cc)
            .setTimestamp();

        // Top staff members
        if (stats.topStaff.length > 0) {
            const staffText = stats.topStaff
                .map((staff, index) => {
                    const user = this.client.users.cache.get(staff.closed_by);
                    const username = user ? user.username : `User ${staff.closed_by}`;
                    return `${index + 1}. **${username}**: ${staff.tickets_closed} tickets`;
                })
                .join('\n');
            embed.addFields({ name: '🏆 Top Staff (Tickets Closed)', value: staffText, inline: false });
        }

        // Bot uptime
        const uptime = process.uptime();
        const days = Math.floor(uptime / 86400);
        const hours = Math.floor((uptime % 86400) / 3600);
        const minutes = Math.floor((uptime % 3600) / 60);
        
        embed.addFields({ 
            name: '🕐 Bot Uptime', 
            value: `${days}d ${hours}h ${minutes}m`, 
            inline: true 
        });

        // Memory usage
        const memUsage = process.memoryUsage();
        const memMB = Math.round(memUsage.heapUsed / 1024 / 1024);
        embed.addFields({ 
            name: '💾 Memory Usage', 
            value: `${memMB} MB`, 
            inline: true 
        });

        return embed;
    }

    async generateCSVReport(stats) {
        const csvLines = [
            'Metric,Value',
            `Total Tickets,${stats.totalTickets.count}`,
            `Open Tickets,${stats.openTickets.count}`,
            `Closed Tickets,${stats.closedTickets.count}`,
            `Tickets Today,${stats.ticketsToday.count}`,
            `Tickets This Week,${stats.ticketsThisWeek.count}`,
            `Tickets This Month,${stats.ticketsThisMonth.count}`,
            `Total Users,${stats.totalUsers.count}`,
            `Blacklisted Users,${stats.blacklistedUsers.count}`,
            `Total Snippets,${stats.totalSnippets.count}`,
            `Snippet Usage,${stats.snippetUsage.total || 0}`,
            '',
            'Category Breakdown',
            'Category,Count'
        ];

        // Add category data
        stats.categoryBreakdown.forEach(cat => {
            csvLines.push(`${cat.category},${cat.count}`);
        });

        csvLines.push('', 'Daily Trends', 'Date,Count');
        
        // Add daily trends
        stats.dailyTrends.forEach(day => {
            csvLines.push(`${day.date},${day.count}`);
        });

        return csvLines.join('\n');
    }

    async getTicketHeatmap(interaction) {
        await interaction.deferReply({ ephemeral: true });

        try {
            // Get hourly ticket creation data for the last 7 days
            const heatmapData = await database.all(`
                SELECT 
                    strftime('%H', created_at) as hour,
                    strftime('%w', created_at) as day_of_week,
                    COUNT(*) as count
                FROM tickets 
                WHERE created_at >= datetime('now', '-7 days')
                GROUP BY hour, day_of_week
                ORDER BY day_of_week, hour
            `);

            const embed = new EmbedBuilder()
                .setTitle('🔥 Ticket Creation Heatmap (Last 7 Days)')
                .setColor(0xff4444)
                .setTimestamp();

            // Create a visual heatmap
            const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
            let heatmapText = '```\n     ';
            
            // Header with hours
            for (let h = 0; h < 24; h += 2) {
                heatmapText += h.toString().padStart(2, '0') + ' ';
            }
            heatmapText += '\n';

            // Create heatmap grid
            for (let day = 0; day < 7; day++) {
                heatmapText += days[day] + '  ';
                for (let hour = 0; hour < 24; hour += 2) {
                    const data = heatmapData.find(d => 
                        parseInt(d.day_of_week) === day && parseInt(d.hour) === hour
                    );
                    const count = data ? data.count : 0;
                    const intensity = count === 0 ? '·' : count < 3 ? '▪' : count < 6 ? '▫' : '█';
                    heatmapText += intensity + '  ';
                }
                heatmapText += '\n';
            }
            heatmapText += '\nLegend: · (0) ▪ (1-2) ▫ (3-5) █ (6+)\n```';

            embed.setDescription(heatmapText);

            await interaction.editReply({ embeds: [embed] });

        } catch (error) {
            console.error('Error generating heatmap:', error);
            const embed = EmbedHelper.error('Heatmap Error', 'Failed to generate ticket heatmap.');
            await interaction.editReply({ embeds: [embed] });
        }
    }
}

// Slash command definitions
const commands = [
    new SlashCommandBuilder()
        .setName('analytics')
        .setDescription('Advanced analytics and reporting')
        .addSubcommand(subcommand =>
            subcommand
                .setName('overview')
                .setDescription('Comprehensive analytics overview with CSV export')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('heatmap')
                .setDescription('Ticket creation heatmap showing peak hours')
        )
];

module.exports = {
    AnalyticsManager,
    commands
};

const { Client, GatewayIntentBits, Collection, ActivityType, EmbedBuilder } = require('discord.js');
const fs = require('fs');
const path = require('path');
const chalk = require('chalk');

// Import modules
const config = require('./config/config');
const database = require('./database/database');
const { EmbedHelper } = require('./utils/helpers');
const TaskScheduler = require('./utils/scheduler');

// Import command handlers
const { ModMailHandler } = require('./commands/modmail');
const { AIFeatures } = require('./commands/ai');
const { SnippetManager } = require('./commands/snippets');
const { AdminCommands } = require('./commands/admin');

class ChatterBot extends Client {
    constructor() {
        super({
            intents: [
                GatewayIntentBits.Guilds,
                GatewayIntentBits.GuildMessages,
                GatewayIntentBits.MessageContent,
                GatewayIntentBits.DirectMessages,
                GatewayIntentBits.GuildMembers
            ],
            partials: ['CHANNEL', 'MESSAGE']
        });

        this.commands = new Collection();
        this.startTime = Date.now();
        this.version = '1.0.0';

        // Initialize handlers
        this.modmail = new ModMailHandler(this);
        this.ai = new AIFeatures(this);
        this.snippets = new SnippetManager(this);
        this.admin = new AdminCommands(this);
        this.scheduler = new TaskScheduler(this);

        // Initialize bot
        this.init();
    }

    async init() {
        console.log(chalk.blue('🤖 ChatterBot is starting up...'));

        try {
            // Connect to database
            await database.connect();

            // Load commands
            await this.loadCommands();

            // Load events
            this.loadEvents();

            // Login to Discord
            await this.login(config.discordToken);

        } catch (error) {
            console.error(chalk.red('❌ Failed to start bot:'), error);
            process.exit(1);
        }
    }

    async loadCommands() {
        const commandFiles = [
            './commands/modmail.js',
            './commands/ai.js',
            './commands/snippets.js',
            './commands/admin.js'
        ];

        for (const file of commandFiles) {
            try {
                const { commands } = require(file);
                if (commands && Array.isArray(commands)) {
                    for (const command of commands) {
                        this.commands.set(command.name, command);
                    }
                    console.log(chalk.green(`✅ Loaded commands from ${file}`));
                }
            } catch (error) {
                console.error(chalk.red(`❌ Failed to load ${file}:`), error);
            }
        }
    }

    loadEvents() {
        // Ready event
        this.once('ready', async () => {
            console.log(chalk.green(`🎉 ${this.user.tag} is now online!`));
            console.log(chalk.blue(`📊 Connected to ${this.guilds.cache.size} guilds`));
            console.log(chalk.blue(`👥 Serving ${this.users.cache.size} users`));

            // Set bot status
            const activityType = config.get('bot_settings.activity_type', 'WATCHING');
            const statusText = config.get('bot_settings.status', 'DM me for support!');

            const activityTypes = {
                'PLAYING': ActivityType.Playing,
                'WATCHING': ActivityType.Watching,
                'LISTENING': ActivityType.Listening,
                'STREAMING': ActivityType.Streaming
            };

            await this.user.setActivity(statusText, { 
                type: activityTypes[activityType] || ActivityType.Watching 
            });

            console.log(chalk.green(`✅ Status set to: ${activityType} ${statusText}`));

            // Start scheduler
            this.scheduler.start();

            // Register slash commands
            await this.registerSlashCommands();
        });

        // Message events
        this.on('messageCreate', async (message) => {
            // Ignore bot messages
            if (message.author.bot) return;

            try {
                // Handle DMs
                if (message.channel.type === 1) { // DM Channel
                    await this.modmail.handleDM(message);
                }
                // Handle ticket channel messages
                else if (message.channel.parentId === config.get('modmail.category_id')) {
                    await this.modmail.handleTicketMessage(message);
                }
            } catch (error) {
                console.error('Error handling message:', error);
            }
        });

        // Interaction events
        this.on('interactionCreate', async (interaction) => {
            try {
                if (interaction.isChatInputCommand()) {
                    await this.handleSlashCommand(interaction);
                } else if (interaction.isButton()) {
                    await this.handleButtonInteraction(interaction);
                } else if (interaction.isModalSubmit()) {
                    await this.handleModalSubmit(interaction);
                }
            } catch (error) {
                console.error('Error handling interaction:', error);
                
                if (!interaction.replied && !interaction.deferred) {
                    await interaction.reply({ 
                        content: '❌ An error occurred while processing your request.', 
                        ephemeral: true 
                    });
                }
            }
        });

        // Guild events
        this.on('guildCreate', async (guild) => {
            console.log(chalk.green(`📥 Joined guild: ${guild.name} (${guild.id})`));
            await this.sendWelcomeMessage(guild);
        });

        this.on('guildDelete', (guild) => {
            console.log(chalk.yellow(`📤 Left guild: ${guild.name} (${guild.id})`));
        });

        // Error handling
        this.on('error', (error) => {
            console.error(chalk.red('Discord client error:'), error);
        });

        this.on('warn', (warning) => {
            console.warn(chalk.yellow('Discord client warning:'), warning);
        });

        process.on('unhandledRejection', (error) => {
            console.error(chalk.red('Unhandled promise rejection:'), error);
        });
    }

    async registerSlashCommands() {
        try {
            const commandsArray = Array.from(this.commands.values());
            
            if (config.guildId) {
                // Register to specific guild for development
                const guild = this.guilds.cache.get(config.guildId);
                if (guild) {
                    await guild.commands.set(commandsArray);
                    console.log(chalk.green(`✅ Registered ${commandsArray.length} slash commands to guild ${guild.name}`));
                }
            } else {
                // Register globally for production
                await this.application.commands.set(commandsArray);
                console.log(chalk.green(`✅ Registered ${commandsArray.length} slash commands globally`));
            }
        } catch (error) {
            console.error(chalk.red('❌ Failed to register slash commands:'), error);
        }
    }

    async handleSlashCommand(interaction) {
        const { commandName } = interaction;

        switch (commandName) {
            // ModMail commands
            case 'close':
                await this.modmail.closeTicket(interaction, interaction.options.getString('reason'));
                break;
            case 'aclose':
                await this.modmail.closeTicket(interaction, interaction.options.getString('reason'), true);
                break;
            case 'areply':
                await this.admin.anonymousReply(interaction);
                break;

            // AI commands
            case 'aireply':
                await this.ai.generateAIReply(interaction);
                break;
            case 'summarize':
                await this.ai.summarizeTicket(interaction);
                break;
            case 'translate':
                await this.ai.translateMessage(interaction);
                break;

            // Snippet commands
            case 'snippet':
                await this.snippets.useSnippet(interaction);
                break;
            case 'snippets':
                const subcommand = interaction.options.getSubcommand();
                switch (subcommand) {
                    case 'list':
                        await this.snippets.listSnippets(interaction);
                        break;
                    case 'create':
                        await this.snippets.createSnippet(interaction);
                        break;
                    case 'delete':
                        await this.snippets.deleteSnippet(interaction);
                        break;
                    case 'info':
                        await this.snippets.showSnippetInfo(interaction);
                        break;
                    case 'search':
                        await this.snippets.searchSnippets(interaction);
                        break;
                }
                break;

            // Admin commands
            case 'blacklist':
                const blacklistSub = interaction.options.getSubcommand();
                switch (blacklistSub) {
                    case 'add':
                        await this.admin.blacklistUser(interaction);
                        break;
                    case 'remove':
                        await this.admin.unblacklistUser(interaction);
                        break;
                    case 'check':
                        await this.admin.checkBlacklist(interaction);
                        break;
                    case 'list':
                        await this.admin.listBlacklist(interaction);
                        break;
                }
                break;
            case 'closeall':
                await this.admin.closeAllTickets(interaction);
                break;
            case 'schedule':
                await this.admin.scheduleClose(interaction);
                break;
            case 'stats':
                await this.admin.showStats(interaction);
                break;
        }
    }

    async handleButtonInteraction(interaction) {
        const customId = interaction.customId;

        if (customId.startsWith('close_ticket_')) {
            // Handle ticket close button
            await this.modmail.closeTicket(interaction);
        } else if (customId.startsWith('assign_ticket_')) {
            // Handle ticket assignment
            await interaction.reply({ content: '✅ Ticket assigned to you!', ephemeral: true });
        } else if (customId.startsWith('confirm_') || customId.startsWith('cancel_')) {
            // Handle confirmation buttons
            if (customId.includes('closeall')) {
                await this.admin.handleCloseAllConfirmation(interaction);
            } else if (customId.includes('delete_snippet_')) {
                const snippetName = customId.split('_').slice(2).join('_');
                await this.snippets.handleDeleteConfirmation(interaction, snippetName);
            }
        } else if (customId.includes('ai_response_')) {
            // Handle AI response buttons
            await this.ai.handleAIResponseButton(interaction);
        }
    }

    async handleModalSubmit(interaction) {
        if (interaction.customId === 'create_snippet_modal') {
            await this.snippets.handleCreateSnippetModal(interaction);
        }
    }

    async sendWelcomeMessage(guild) {
        // Find a suitable channel to send welcome message
        let channel = guild.systemChannel;
        
        if (!channel) {
            // Find first text channel bot can send messages to
            channel = guild.channels.cache
                .filter(ch => ch.type === 0 && ch.permissionsFor(guild.members.me).has('SendMessages'))
                .first();
        }

        if (channel) {
            const embed = EmbedHelper.info(
                'Thanks for adding ChatterBot!',
                'I\'m an advanced ModMail bot that helps you manage support tickets efficiently.'
            )
            .addFields(
                {
                    name: '🚀 Getting Started',
                    value: '1. Create a category for tickets\n2. Set up your configuration\n3. Configure staff roles\n4. Users can now DM me to create tickets!',
                    inline: false
                },
                {
                    name: '📚 Features',
                    value: '• Advanced ticket management\n• AI-powered responses with Puter.js\n• Snippet system for quick replies\n• User blacklisting\n• Scheduled ticket closing\n• Anonymous staff replies',
                    inline: false
                },
                {
                    name: '🔗 Support',
                    value: 'Need help? Check the documentation or join our support server!',
                    inline: false
                }
            );

            try {
                await channel.send({ embeds: [embed] });
            } catch (error) {
                console.error('Error sending welcome message:', error);
            }
        }
    }

    async shutdown() {
        console.log(chalk.yellow('🔄 ChatterBot is shutting down...'));
        
        this.scheduler.stop();
        await database.close();
        
        this.destroy();
        process.exit(0);
    }
}

// Create and start the bot
const bot = new ChatterBot();

// Graceful shutdown
process.on('SIGINT', () => bot.shutdown());
process.on('SIGTERM', () => bot.shutdown());

module.exports = ChatterBot;

const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs-extra');
const config = require('../config/config');

class Database {
    constructor() {
        this.dbPath = config.databasePath;
        this.db = null;
    }

    async connect() {
        try {
            // Ensure database directory exists
            fs.ensureDirSync(path.dirname(this.dbPath));
            
            this.db = new sqlite3.Database(this.dbPath);
            await this.createTables();
            console.log('✅ Database connected');
        } catch (error) {
            console.error('❌ Database connection failed:', error);
            throw error;
        }
    }

    async createTables() {
        const tables = [
            // Tickets table
            `CREATE TABLE IF NOT EXISTS tickets (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                channel_id TEXT UNIQUE,
                guild_id TEXT NOT NULL,
                status TEXT DEFAULT 'open',
                category TEXT DEFAULT 'general',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                closed_at DATETIME,
                closed_by TEXT,
                close_reason TEXT,
                is_anonymous_close BOOLEAN DEFAULT FALSE,
                scheduled_close_at DATETIME,
                priority INTEGER DEFAULT 1,
                assigned_to TEXT,
                last_activity DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,
            
            // Messages table for logging
            `CREATE TABLE IF NOT EXISTS messages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ticket_id INTEGER,
                message_id TEXT,
                user_id TEXT NOT NULL,
                content TEXT,
                attachments TEXT,
                is_staff BOOLEAN DEFAULT FALSE,
                is_anonymous BOOLEAN DEFAULT FALSE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (ticket_id) REFERENCES tickets (id)
            )`,
            
            // Snippets table
            `CREATE TABLE IF NOT EXISTS snippets (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                content TEXT NOT NULL,
                created_by TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                usage_count INTEGER DEFAULT 0,
                category TEXT DEFAULT 'general'
            )`,
            
            // User settings and blacklist
            `CREATE TABLE IF NOT EXISTS users (
                user_id TEXT PRIMARY KEY,
                is_blacklisted BOOLEAN DEFAULT FALSE,
                blacklist_reason TEXT,
                blacklisted_by TEXT,
                blacklisted_at DATETIME,
                total_tickets INTEGER DEFAULT 0,
                last_ticket_at DATETIME,
                notes TEXT
            )`,
            
            // Staff actions log
            `CREATE TABLE IF NOT EXISTS staff_actions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                staff_id TEXT NOT NULL,
                action_type TEXT NOT NULL,
                target_id TEXT,
                details TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,
            
            // Scheduled tasks
            `CREATE TABLE IF NOT EXISTS scheduled_tasks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_type TEXT NOT NULL,
                target_id TEXT NOT NULL,
                scheduled_for DATETIME NOT NULL,
                data TEXT,
                completed BOOLEAN DEFAULT FALSE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,
            
            // Ticket categories
            `CREATE TABLE IF NOT EXISTS ticket_categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                description TEXT,
                emoji TEXT,
                ping_role_id TEXT,
                auto_response TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Auto-responders
            `CREATE TABLE IF NOT EXISTS auto_responders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                triggers TEXT NOT NULL,
                response TEXT NOT NULL,
                conditions TEXT DEFAULT '{}',
                created_by TEXT NOT NULL,
                enabled BOOLEAN DEFAULT TRUE,
                usage_count INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Ticket ratings and feedback
            `CREATE TABLE IF NOT EXISTS ticket_feedback (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ticket_id INTEGER NOT NULL,
                user_id TEXT NOT NULL,
                rating INTEGER CHECK(rating >= 1 AND rating <= 5),
                feedback TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (ticket_id) REFERENCES tickets (id)
            )`,

            // SLA tracking
            `CREATE TABLE IF NOT EXISTS sla_tracking (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ticket_id INTEGER NOT NULL,
                first_response_time INTEGER,
                resolution_time INTEGER,
                sla_breached BOOLEAN DEFAULT FALSE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (ticket_id) REFERENCES tickets (id)
            )`
        ];

        for (const table of tables) {
            await this.run(table);
        }
    }

    run(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.run(sql, params, function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ id: this.lastID, changes: this.changes });
                }
            });
        });
    }

    get(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.get(sql, params, (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    all(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    // Ticket operations
    async createTicket(userId, channelId, guildId, category = 'general') {
        const result = await this.run(
            'INSERT INTO tickets (user_id, channel_id, guild_id, category) VALUES (?, ?, ?, ?)',
            [userId, channelId, guildId, category]
        );

        // Update user stats
        await this.run(`
            INSERT OR REPLACE INTO users (user_id, total_tickets, last_ticket_at)
            VALUES (?, COALESCE((SELECT total_tickets FROM users WHERE user_id = ?), 0) + 1, ?)
        `, [userId, userId, new Date().toISOString()]);

        return result.id;
    }

    async getTicket(channelId) {
        return await this.get(
            'SELECT * FROM tickets WHERE channel_id = ? AND status = "open"',
            [channelId]
        );
    }

    async getUserTickets(userId, status = 'open') {
        return await this.all(
            'SELECT * FROM tickets WHERE user_id = ? AND status = ? ORDER BY created_at DESC',
            [userId, status]
        );
    }

    async closeTicket(channelId, closedBy, reason = null, isAnonymous = false) {
        const result = await this.run(`
            UPDATE tickets 
            SET status = 'closed', closed_at = ?, closed_by = ?, 
                close_reason = ?, is_anonymous_close = ?
            WHERE channel_id = ? AND status = 'open'
        `, [new Date().toISOString(), closedBy, reason, isAnonymous, channelId]);

        return result.changes > 0;
    }

    async updateTicketActivity(channelId) {
        await this.run(
            'UPDATE tickets SET last_activity = ? WHERE channel_id = ?',
            [new Date().toISOString(), channelId]
        );
    }

    // Message operations
    async logMessage(ticketId, messageId, userId, content, attachments = null, isStaff = false, isAnonymous = false) {
        const attachmentsJson = attachments ? JSON.stringify(attachments) : null;
        
        await this.run(`
            INSERT INTO messages (ticket_id, message_id, user_id, content, 
                                attachments, is_staff, is_anonymous)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        `, [ticketId, messageId, userId, content, attachmentsJson, isStaff, isAnonymous]);
    }

    // Snippet operations
    async createSnippet(name, content, createdBy, category = 'general') {
        try {
            await this.run(
                'INSERT INTO snippets (name, content, created_by, category) VALUES (?, ?, ?, ?)',
                [name, content, createdBy, category]
            );
            return true;
        } catch (error) {
            if (error.code === 'SQLITE_CONSTRAINT') {
                return false; // Snippet already exists
            }
            throw error;
        }
    }

    async getSnippet(name) {
        const snippet = await this.get('SELECT * FROM snippets WHERE name = ?', [name]);
        
        if (snippet) {
            // Increment usage count
            await this.run('UPDATE snippets SET usage_count = usage_count + 1 WHERE name = ?', [name]);
        }
        
        return snippet;
    }

    async listSnippets(category = null) {
        if (category) {
            return await this.all(
                'SELECT name, category, usage_count FROM snippets WHERE category = ? ORDER BY usage_count DESC',
                [category]
            );
        } else {
            return await this.all(
                'SELECT name, category, usage_count FROM snippets ORDER BY usage_count DESC'
            );
        }
    }

    // User management
    async blacklistUser(userId, reason, blacklistedBy) {
        await this.run(`
            INSERT OR REPLACE INTO users 
            (user_id, is_blacklisted, blacklist_reason, blacklisted_by, blacklisted_at)
            VALUES (?, TRUE, ?, ?, ?)
        `, [userId, reason, blacklistedBy, new Date().toISOString()]);
        
        return true;
    }

    async unblacklistUser(userId) {
        const result = await this.run(`
            UPDATE users SET is_blacklisted = FALSE, blacklist_reason = NULL,
                           blacklisted_by = NULL, blacklisted_at = NULL
            WHERE user_id = ?
        `, [userId]);
        
        return result.changes > 0;
    }

    async isBlacklisted(userId) {
        const user = await this.get('SELECT is_blacklisted FROM users WHERE user_id = ?', [userId]);
        return user ? user.is_blacklisted : false;
    }

    // Scheduled tasks
    async scheduleTask(taskType, targetId, scheduledFor, data = null) {
        const dataJson = data ? JSON.stringify(data) : null;
        
        await this.run(
            'INSERT INTO scheduled_tasks (task_type, target_id, scheduled_for, data) VALUES (?, ?, ?, ?)',
            [taskType, targetId, scheduledFor.toISOString(), dataJson]
        );
    }

    async getDueTasks() {
        return await this.all(
            'SELECT * FROM scheduled_tasks WHERE scheduled_for <= ? AND completed = FALSE',
            [new Date().toISOString()]
        );
    }

    async completeTask(taskId) {
        await this.run('UPDATE scheduled_tasks SET completed = TRUE WHERE id = ?', [taskId]);
    }

    async close() {
        if (this.db) {
            this.db.close();
        }
    }
}

module.exports = new Database();

const { <PERSON><PERSON><PERSON><PERSON>mand<PERSON><PERSON>er, ModalBuilder, TextInputBuilder, TextInputStyle, ActionRowBuilder } = require('discord.js');
const database = require('../database/database');
const { Embed<PERSON><PERSON>per, ComponentHelper, safeReply, chunkArray } = require('../utils/helpers');
const { requireTicketManager } = require('../utils/permissions');

class SnippetManager {
    constructor(client) {
        this.client = client;
    }

    async useSnippet(interaction) {
        const name = interaction.options.getString('name').toLowerCase();
        
        const snippet = await database.getSnippet(name);
        if (snippet) {
            // Send snippet content
            await interaction.reply({ content: snippet.content, allowedMentions: { parse: [] } });
            
            // Delete the command message if possible
            try {
                if (interaction.message) {
                    await interaction.message.delete();
                }
            } catch (error) {
                // Ignore deletion errors
            }
        } else {
            const embed = EmbedHelper.error('Snippet Not Found', `No snippet found with the name \`${name}\`.`);
            await safeReply(interaction, { embeds: [embed], ephemeral: true });
        }
    }

    async listSnippets(interaction) {
        const category = interaction.options.getString('category');
        const snippets = await database.listSnippets(category);

        if (snippets.length === 0) {
            const message = category 
                ? `No snippets found in category \`${category}\`.`
                : 'No snippets have been created yet.';
            const embed = EmbedHelper.warning('No Snippets', message);
            await safeReply(interaction, { embeds: [embed], ephemeral: true });
            return;
        }

        // Group snippets by category
        const categories = {};
        for (const snippet of snippets) {
            const cat = snippet.category;
            if (!categories[cat]) {
                categories[cat] = [];
            }
            categories[cat].push(snippet);
        }

        // Create embeds for each category
        const embeds = [];
        for (const [cat, catSnippets] of Object.entries(categories)) {
            const embed = EmbedHelper.info(`Snippets - ${cat.charAt(0).toUpperCase() + cat.slice(1)}`, '');
            
            const snippetList = catSnippets.map(snippet => 
                `\`${snippet.name}\` (used ${snippet.usage_count} times)`
            );
            
            embed.setDescription(snippetList.join('\n'));
            embed.setFooter({ text: `Total: ${catSnippets.length} snippets` });
            embeds.push(embed);
        }

        if (embeds.length === 1) {
            await safeReply(interaction, { embeds: [embeds[0]], ephemeral: true });
        } else {
            // For multiple categories, send the first one with pagination buttons
            const buttons = ComponentHelper.createPaginationButtons(0, embeds.length);
            await safeReply(interaction, { embeds: [embeds[0]], components: [buttons], ephemeral: true });
            
            // Store embeds for pagination (in production, use Redis or similar)
            this.client.paginationData = this.client.paginationData || new Map();
            this.client.paginationData.set(interaction.user.id, {
                embeds: embeds,
                currentPage: 0,
                messageId: null // Will be set after reply
            });
        }
    }

    async createSnippet(interaction) {
        // Create modal for snippet creation
        const modal = new ModalBuilder()
            .setCustomId('create_snippet_modal')
            .setTitle('Create New Snippet');

        const nameInput = new TextInputBuilder()
            .setCustomId('snippet_name')
            .setLabel('Snippet Name')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Enter a unique name for this snippet...')
            .setRequired(true)
            .setMaxLength(50);

        const categoryInput = new TextInputBuilder()
            .setCustomId('snippet_category')
            .setLabel('Category')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('general, support, rules, etc.')
            .setRequired(false)
            .setMaxLength(30)
            .setValue('general');

        const contentInput = new TextInputBuilder()
            .setCustomId('snippet_content')
            .setLabel('Snippet Content')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('Enter the snippet content...')
            .setRequired(true)
            .setMaxLength(2000);

        const firstRow = new ActionRowBuilder().addComponents(nameInput);
        const secondRow = new ActionRowBuilder().addComponents(categoryInput);
        const thirdRow = new ActionRowBuilder().addComponents(contentInput);

        modal.addComponents(firstRow, secondRow, thirdRow);

        await interaction.showModal(modal);
    }

    async handleCreateSnippetModal(interaction) {
        const name = interaction.fields.getTextInputValue('snippet_name').toLowerCase();
        const category = interaction.fields.getTextInputValue('snippet_category') || 'general';
        const content = interaction.fields.getTextInputValue('snippet_content');

        const success = await database.createSnippet(name, content, interaction.user.id, category);

        if (success) {
            const embed = EmbedHelper.success(
                'Snippet Created',
                `Snippet \`${name}\` has been created successfully!`
            );
            await safeReply(interaction, { embeds: [embed], ephemeral: true });
        } else {
            const embed = EmbedHelper.error(
                'Snippet Exists',
                `A snippet with the name \`${name}\` already exists.`
            );
            await safeReply(interaction, { embeds: [embed], ephemeral: true });
        }
    }

    async deleteSnippet(interaction) {
        const name = interaction.options.getString('name').toLowerCase();
        
        // Check if snippet exists
        const snippet = await database.getSnippet(name);
        if (!snippet) {
            const embed = EmbedHelper.error('Snippet Not Found', `No snippet found with the name \`${name}\`.`);
            await safeReply(interaction, { embeds: [embed], ephemeral: true });
            return;
        }

        // Create confirmation buttons
        const buttons = ComponentHelper.createConfirmationButtons(`delete_snippet_${name}`);
        const embed = EmbedHelper.warning(
            'Confirm Deletion',
            `Are you sure you want to delete the snippet \`${name}\`?`
        );

        await safeReply(interaction, { embeds: [embed], components: [buttons], ephemeral: true });
    }

    async handleDeleteConfirmation(interaction, snippetName) {
        const [action] = interaction.customId.split('_');
        
        if (action === 'confirm') {
            // Delete snippet (would need database method)
            try {
                await database.run('DELETE FROM snippets WHERE name = ?', [snippetName]);
                const embed = EmbedHelper.success('Snippet Deleted', `Snippet \`${snippetName}\` has been deleted.`);
                await interaction.update({ embeds: [embed], components: [] });
            } catch (error) {
                const embed = EmbedHelper.error('Delete Failed', 'Failed to delete snippet.');
                await interaction.update({ embeds: [embed], components: [] });
            }
        } else {
            const embed = EmbedHelper.info('Deletion Cancelled', `Snippet \`${snippetName}\` was not deleted.`);
            await interaction.update({ embeds: [embed], components: [] });
        }
    }

    async showSnippetInfo(interaction) {
        const name = interaction.options.getString('name').toLowerCase();
        const snippet = await database.getSnippet(name);
        
        if (!snippet) {
            const embed = EmbedHelper.error('Snippet Not Found', `No snippet found with the name \`${name}\`.`);
            await safeReply(interaction, { embeds: [embed], ephemeral: true });
            return;
        }

        const embed = EmbedHelper.info(`Snippet: ${snippet.name}`, '')
            .addFields(
                { name: 'Category', value: snippet.category, inline: true },
                { name: 'Usage Count', value: snippet.usage_count.toString(), inline: true },
                { name: 'Created', value: `<t:${Math.floor(new Date(snippet.created_at).getTime() / 1000)}:R>`, inline: true },
                { name: 'Content', value: snippet.content.substring(0, 1000), inline: false }
            );

        if (snippet.content.length > 1000) {
            embed.setFooter({ text: 'Content truncated...' });
        }

        await safeReply(interaction, { embeds: [embed], ephemeral: true });
    }

    async searchSnippets(interaction) {
        const query = interaction.options.getString('query').toLowerCase();
        const allSnippets = await database.listSnippets();

        // Filter snippets by query
        const matchingSnippets = allSnippets.filter(snippet =>
            snippet.name.toLowerCase().includes(query) ||
            snippet.content.toLowerCase().includes(query)
        );

        if (matchingSnippets.length === 0) {
            const embed = EmbedHelper.warning('No Results', `No snippets found matching \`${query}\`.`);
            await safeReply(interaction, { embeds: [embed], ephemeral: true });
            return;
        }

        const embed = EmbedHelper.info(`Search Results for '${query}'`, '');
        
        const results = matchingSnippets.slice(0, 10).map(snippet =>
            `\`${snippet.name}\` - ${snippet.content.substring(0, 50)}...`
        );

        embed.setDescription(results.join('\n'));
        embed.setFooter({ text: `Showing ${results.length} of ${matchingSnippets.length} results` });

        await safeReply(interaction, { embeds: [embed], ephemeral: true });
    }
}

// Slash command definitions
const commands = [
    new SlashCommandBuilder()
        .setName('snippet')
        .setDescription('Use a snippet')
        .addStringOption(option =>
            option.setName('name')
                .setDescription('Name of the snippet to use')
                .setRequired(true)
        ),
    
    new SlashCommandBuilder()
        .setName('snippets')
        .setDescription('Manage snippets')
        .addSubcommand(subcommand =>
            subcommand
                .setName('list')
                .setDescription('List all snippets')
                .addStringOption(option =>
                    option.setName('category')
                        .setDescription('Filter by category')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('create')
                .setDescription('Create a new snippet')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('delete')
                .setDescription('Delete a snippet')
                .addStringOption(option =>
                    option.setName('name')
                        .setDescription('Name of the snippet to delete')
                        .setRequired(true)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('info')
                .setDescription('Show information about a snippet')
                .addStringOption(option =>
                    option.setName('name')
                        .setDescription('Name of the snippet')
                        .setRequired(true)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('search')
                .setDescription('Search snippets by name or content')
                .addStringOption(option =>
                    option.setName('query')
                        .setDescription('Search query')
                        .setRequired(true)
                )
        )
];

module.exports = {
    SnippetManager,
    commands
};

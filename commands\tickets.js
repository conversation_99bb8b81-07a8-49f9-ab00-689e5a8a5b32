const { <PERSON><PERSON><PERSON><PERSON>mandB<PERSON>er, <PERSON><PERSON><PERSON><PERSON>er, StringSelectMenuBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const database = require('../database/database');
const config = require('../config/config');
const { EmbedHelper, safeReply } = require('../utils/helpers');
const { requireTicketManager } = require('../utils/permissions');

class TicketManager {
    constructor(client) {
        this.client = client;
    }

    async createTicketPanel(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('🎫 Create Support Ticket')
            .setDescription('Select a category below to create a new support ticket. Our team will assist you shortly!')
            .setColor(0x0099ff)
            .addFields(
                { name: '📋 General Support', value: 'General questions and assistance', inline: true },
                { name: '💳 Billing', value: 'Payment and subscription issues', inline: true },
                { name: '🐛 Bug Report', value: 'Report technical issues', inline: true },
                { name: '💡 Feature Request', value: 'Suggest new features', inline: true },
                { name: '🔧 Technical Support', value: 'Technical troubleshooting', inline: true },
                { name: '❓ Other', value: 'Other inquiries', inline: true }
            )
            .setFooter({ text: 'Click the dropdown below to select a category' });

        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId('ticket_category_select')
            .setPlaceholder('Choose a ticket category...')
            .addOptions([
                {
                    label: 'General Support',
                    description: 'General questions and assistance',
                    value: 'general',
                    emoji: '📋'
                },
                {
                    label: 'Billing',
                    description: 'Payment and subscription issues',
                    value: 'billing',
                    emoji: '💳'
                },
                {
                    label: 'Bug Report',
                    description: 'Report technical issues',
                    value: 'bug',
                    emoji: '🐛'
                },
                {
                    label: 'Feature Request',
                    description: 'Suggest new features',
                    value: 'feature',
                    emoji: '💡'
                },
                {
                    label: 'Technical Support',
                    description: 'Technical troubleshooting',
                    value: 'technical',
                    emoji: '🔧'
                },
                {
                    label: 'Other',
                    description: 'Other inquiries',
                    value: 'other',
                    emoji: '❓'
                }
            ]);

        const row = new ActionRowBuilder().addComponents(selectMenu);

        await interaction.reply({ embeds: [embed], components: [row] });
    }

    async handleCategorySelect(interaction) {
        const category = interaction.values[0];
        const user = interaction.user;

        // Check if user is blacklisted
        if (await database.isBlacklisted(user.id)) {
            const embed = EmbedHelper.error('Blacklisted', config.get('messages.blacklist_message'));
            await interaction.reply({ embeds: [embed], ephemeral: true });
            return;
        }

        // Check ticket limits
        const openTickets = await database.getUserTickets(user.id, 'open');
        const maxTickets = config.get('modmail.max_tickets_per_user', 3);

        if (openTickets.length >= maxTickets) {
            const embed = EmbedHelper.warning(
                'Too Many Tickets',
                `You have reached the maximum number of open tickets (${maxTickets}). Please wait for your current tickets to be resolved.`
            );
            await interaction.reply({ embeds: [embed], ephemeral: true });
            return;
        }

        // Create ticket
        try {
            const guild = interaction.guild;
            const categoryChannel = guild.channels.cache.get(config.get('modmail.category_id'));
            
            if (!categoryChannel) {
                const embed = EmbedHelper.error('Configuration Error', 'ModMail category not found.');
                await interaction.reply({ embeds: [embed], ephemeral: true });
                return;
            }

            // Create ticket channel
            const channelName = `${category}-${user.username}-${user.discriminator}`;
            const channel = await guild.channels.create({
                name: channelName,
                parent: categoryChannel.id,
                topic: `${category.charAt(0).toUpperCase() + category.slice(1)} ticket for ${user.tag} (${user.id})`
            });

            // Create ticket in database
            const ticketId = await database.createTicket(user.id, channel.id, guild.id, category);

            // Set up ticket channel
            await this.setupAdvancedTicketChannel(channel, user, ticketId, category);

            // Send confirmation to user
            const embed = EmbedHelper.success(
                'Ticket Created',
                `Your ${category} ticket has been created! Check ${channel} for updates.`
            );
            await interaction.reply({ embeds: [embed], ephemeral: true });

            // Log ticket creation
            const logChannel = guild.channels.cache.get(config.get('modmail.log_channel_id'));
            if (logChannel) {
                const logEmbed = EmbedHelper.ticketCreated(user, ticketId, category);
                await logChannel.send({ embeds: [logEmbed] });
            }

        } catch (error) {
            console.error('Error creating ticket:', error);
            const embed = EmbedHelper.error('Creation Failed', 'Failed to create ticket. Please try again.');
            await interaction.reply({ embeds: [embed], ephemeral: true });
        }
    }

    async setupAdvancedTicketChannel(channel, user, ticketId, category) {
        // Create comprehensive ticket info embed
        const embed = new EmbedBuilder()
            .setTitle(`🎫 ${category.charAt(0).toUpperCase() + category.slice(1)} Support Ticket`)
            .setDescription(`Ticket created for ${user}`)
            .setColor(this.getCategoryColor(category))
            .addFields(
                { name: '👤 User', value: `${user} (${user.id})`, inline: true },
                { name: '📂 Category', value: category.charAt(0).toUpperCase() + category.slice(1), inline: true },
                { name: '🆔 Ticket ID', value: `#${ticketId}`, inline: true },
                { name: '📅 Created', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true },
                { name: '⏰ Status', value: '🟢 Open', inline: true },
                { name: '👥 Assigned', value: 'Unassigned', inline: true }
            )
            .setThumbnail(user.displayAvatarURL())
            .setTimestamp();

        // Add category-specific information
        const categoryInfo = this.getCategoryInfo(category);
        if (categoryInfo) {
            embed.addFields({ name: '📋 Category Info', value: categoryInfo, inline: false });
        }

        // Create action buttons
        const actionRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`claim_ticket_${ticketId}`)
                    .setLabel('Claim Ticket')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('👤'),
                new ButtonBuilder()
                    .setCustomId(`priority_ticket_${ticketId}`)
                    .setLabel('Set Priority')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⚡'),
                new ButtonBuilder()
                    .setCustomId(`close_ticket_${ticketId}`)
                    .setLabel('Close Ticket')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('🔒')
            );

        // Create priority select menu
        const prioritySelect = new StringSelectMenuBuilder()
            .setCustomId(`priority_select_${ticketId}`)
            .setPlaceholder('Set ticket priority...')
            .addOptions([
                { label: 'Low Priority', value: 'low', emoji: '🟢' },
                { label: 'Normal Priority', value: 'normal', emoji: '🟡' },
                { label: 'High Priority', value: 'high', emoji: '🟠' },
                { label: 'Urgent', value: 'urgent', emoji: '🔴' }
            ]);

        const selectRow = new ActionRowBuilder().addComponents(prioritySelect);

        await channel.send({ embeds: [embed], components: [actionRow, selectRow] });

        // Send category-specific auto-response
        const autoResponse = this.getCategoryAutoResponse(category);
        if (autoResponse) {
            const responseEmbed = new EmbedBuilder()
                .setDescription(autoResponse)
                .setColor(0x00ff00)
                .setAuthor({ name: 'Auto-Response', iconURL: this.client.user.displayAvatarURL() });
            
            await channel.send({ embeds: [responseEmbed] });
        }

        // Ping appropriate role
        const roleId = this.getCategoryRole(category);
        if (roleId) {
            const role = channel.guild.roles.cache.get(roleId);
            if (role) {
                await channel.send(`${role} New ${category} ticket created!`);
            }
        }
    }

    getCategoryColor(category) {
        const colors = {
            general: 0x0099ff,
            billing: 0x00ff00,
            bug: 0xff0000,
            feature: 0x9932cc,
            technical: 0xff9900,
            other: 0x808080
        };
        return colors[category] || 0x0099ff;
    }

    getCategoryInfo(category) {
        const info = {
            general: 'For general questions, account issues, and basic support needs.',
            billing: 'For payment issues, subscription questions, and billing disputes.',
            bug: 'Please provide detailed steps to reproduce the issue and any error messages.',
            feature: 'Describe your feature idea and how it would benefit users.',
            technical: 'Include system information, error logs, and troubleshooting steps tried.',
            other: 'For inquiries that don\'t fit other categories.'
        };
        return info[category];
    }

    getCategoryAutoResponse(category) {
        const responses = {
            billing: 'Thank you for contacting billing support. Please have your account information ready. A billing specialist will assist you shortly.',
            bug: 'Thank you for reporting this issue. Please provide as much detail as possible including steps to reproduce the problem.',
            technical: 'Our technical team will help resolve your issue. Please be ready to provide system information if requested.',
            feature: 'We appreciate your feature suggestion! Our product team reviews all requests and considers them for future updates.'
        };
        return responses[category];
    }

    getCategoryRole(category) {
        // This would be configurable in a real implementation
        const roleMap = {
            billing: config.get('roles.billing_team'),
            technical: config.get('roles.tech_team'),
            bug: config.get('roles.dev_team')
        };
        return roleMap[category];
    }

    async listUserTickets(interaction) {
        const user = interaction.options.getUser('user') || interaction.user;
        const status = interaction.options.getString('status') || 'all';

        let tickets;
        if (status === 'all') {
            tickets = await database.all(
                'SELECT * FROM tickets WHERE user_id = ? ORDER BY created_at DESC LIMIT 10',
                [user.id]
            );
        } else {
            tickets = await database.getUserTickets(user.id, status);
        }

        if (tickets.length === 0) {
            const embed = EmbedHelper.info(
                'No Tickets Found',
                `${user.username} has no ${status === 'all' ? '' : status} tickets.`
            );
            await safeReply(interaction, { embeds: [embed], ephemeral: true });
            return;
        }

        const embed = new EmbedBuilder()
            .setTitle(`🎫 ${user.username}'s Tickets`)
            .setColor(0x0099ff)
            .setThumbnail(user.displayAvatarURL())
            .setTimestamp();

        const ticketList = tickets.map(ticket => {
            const statusEmoji = ticket.status === 'open' ? '🟢' : '🔴';
            const createdDate = new Date(ticket.created_at).toLocaleDateString();
            return `${statusEmoji} **#${ticket.id}** - ${ticket.category}\nCreated: ${createdDate}`;
        }).join('\n\n');

        embed.setDescription(ticketList);
        embed.setFooter({ text: `Total: ${tickets.length} tickets` });

        await safeReply(interaction, { embeds: [embed], ephemeral: true });
    }

    async transferTicket(interaction) {
        const targetCategory = interaction.options.getString('category');
        
        const ticket = await database.getTicket(interaction.channel.id);
        if (!ticket) {
            const embed = EmbedHelper.error('Not a Ticket', 'This command can only be used in ticket channels.');
            await safeReply(interaction, { embeds: [embed], ephemeral: true });
            return;
        }

        try {
            // Update ticket category in database
            await database.run(
                'UPDATE tickets SET category = ? WHERE id = ?',
                [targetCategory, ticket.id]
            );

            // Update channel name and topic
            const user = this.client.users.cache.get(ticket.user_id);
            const newName = `${targetCategory}-${user.username}-${user.discriminator}`;
            const newTopic = `${targetCategory.charAt(0).toUpperCase() + targetCategory.slice(1)} ticket for ${user.tag} (${user.id})`;

            await interaction.channel.edit({
                name: newName,
                topic: newTopic
            });

            const embed = EmbedHelper.success(
                'Ticket Transferred',
                `Ticket has been transferred to the **${targetCategory}** category.`
            );
            await safeReply(interaction, { embeds: [embed] });

        } catch (error) {
            console.error('Error transferring ticket:', error);
            const embed = EmbedHelper.error('Transfer Failed', 'Failed to transfer ticket.');
            await safeReply(interaction, { embeds: [embed], ephemeral: true });
        }
    }
}

// Slash command definitions
const commands = [
    new SlashCommandBuilder()
        .setName('ticket')
        .setDescription('Advanced ticket management')
        .addSubcommand(subcommand =>
            subcommand
                .setName('panel')
                .setDescription('Create a ticket creation panel')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('list')
                .setDescription('List user tickets')
                .addUserOption(option =>
                    option.setName('user')
                        .setDescription('User to list tickets for (defaults to yourself)')
                        .setRequired(false)
                )
                .addStringOption(option =>
                    option.setName('status')
                        .setDescription('Filter by ticket status')
                        .setRequired(false)
                        .addChoices(
                            { name: 'All', value: 'all' },
                            { name: 'Open', value: 'open' },
                            { name: 'Closed', value: 'closed' }
                        )
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('transfer')
                .setDescription('Transfer ticket to different category')
                .addStringOption(option =>
                    option.setName('category')
                        .setDescription('New category for the ticket')
                        .setRequired(true)
                        .addChoices(
                            { name: 'General', value: 'general' },
                            { name: 'Billing', value: 'billing' },
                            { name: 'Bug Report', value: 'bug' },
                            { name: 'Feature Request', value: 'feature' },
                            { name: 'Technical Support', value: 'technical' },
                            { name: 'Other', value: 'other' }
                        )
                )
        )
];

module.exports = {
    TicketManager,
    commands
};

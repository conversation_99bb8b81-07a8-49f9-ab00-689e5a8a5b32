import discord
import asyncio
import re
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any, Union
from config import config

class EmbedBuilder:
    """Helper class for creating consistent embeds"""
    
    @staticmethod
    def success(title: str, description: str = None) -> discord.Embed:
        """Create a success embed"""
        embed = discord.Embed(
            title=f"✅ {title}",
            description=description,
            color=discord.Color.green(),
            timestamp=datetime.now()
        )
        return embed
    
    @staticmethod
    def error(title: str, description: str = None) -> discord.Embed:
        """Create an error embed"""
        embed = discord.Embed(
            title=f"❌ {title}",
            description=description,
            color=discord.Color.red(),
            timestamp=datetime.now()
        )
        return embed
    
    @staticmethod
    def warning(title: str, description: str = None) -> discord.Embed:
        """Create a warning embed"""
        embed = discord.Embed(
            title=f"⚠️ {title}",
            description=description,
            color=discord.Color.orange(),
            timestamp=datetime.now()
        )
        return embed
    
    @staticmethod
    def info(title: str, description: str = None) -> discord.Embed:
        """Create an info embed"""
        embed = discord.Embed(
            title=f"ℹ️ {title}",
            description=description,
            color=discord.Color.blue(),
            timestamp=datetime.now()
        )
        return embed
    
    @staticmethod
    def ticket_created(user: discord.User, ticket_id: int, category: str = "General") -> discord.Embed:
        """Create embed for new ticket"""
        embed = discord.Embed(
            title="🎫 New Support Ticket",
            description=f"Ticket created by {user.mention}",
            color=discord.Color.blue(),
            timestamp=datetime.now()
        )
        embed.add_field(name="User", value=f"{user} ({user.id})", inline=True)
        embed.add_field(name="Category", value=category, inline=True)
        embed.add_field(name="Ticket ID", value=f"#{ticket_id}", inline=True)
        embed.set_thumbnail(url=user.display_avatar.url)
        return embed
    
    @staticmethod
    def ticket_closed(user: discord.User, staff: discord.User, reason: str = None, 
                     is_anonymous: bool = False) -> discord.Embed:
        """Create embed for closed ticket"""
        embed = discord.Embed(
            title="🔒 Ticket Closed",
            color=discord.Color.red(),
            timestamp=datetime.now()
        )
        
        if is_anonymous:
            embed.add_field(name="Closed by", value="Staff Member", inline=True)
        else:
            embed.add_field(name="Closed by", value=f"{staff} ({staff.id})", inline=True)
        
        embed.add_field(name="User", value=f"{user} ({user.id})", inline=True)
        
        if reason:
            embed.add_field(name="Reason", value=reason, inline=False)
        
        return embed

class MessageFormatter:
    """Helper class for formatting messages"""
    
    @staticmethod
    def format_message(template: str, **kwargs) -> str:
        """Format message template with variables"""
        return template.format(**kwargs)
    
    @staticmethod
    def truncate_text(text: str, max_length: int = 2000) -> str:
        """Truncate text to fit Discord message limits"""
        if len(text) <= max_length:
            return text
        return text[:max_length - 3] + "..."
    
    @staticmethod
    def clean_content(content: str) -> str:
        """Clean message content for logging"""
        # Remove mentions and clean up formatting
        content = re.sub(r'<@!?(\d+)>', r'@\1', content)
        content = re.sub(r'<#(\d+)>', r'#\1', content)
        content = re.sub(r'<@&(\d+)>', r'@role\1', content)
        return content

class TimeParser:
    """Helper class for parsing time strings"""
    
    TIME_REGEX = re.compile(r'(\d+)\s*(s|sec|second|seconds|m|min|minute|minutes|h|hour|hours|d|day|days|w|week|weeks)')
    
    @staticmethod
    def parse_time(time_str: str) -> Optional[timedelta]:
        """Parse time string into timedelta"""
        if not time_str:
            return None
        
        total_seconds = 0
        matches = TimeParser.TIME_REGEX.findall(time_str.lower())
        
        for amount, unit in matches:
            amount = int(amount)
            
            if unit in ['s', 'sec', 'second', 'seconds']:
                total_seconds += amount
            elif unit in ['m', 'min', 'minute', 'minutes']:
                total_seconds += amount * 60
            elif unit in ['h', 'hour', 'hours']:
                total_seconds += amount * 3600
            elif unit in ['d', 'day', 'days']:
                total_seconds += amount * 86400
            elif unit in ['w', 'week', 'weeks']:
                total_seconds += amount * 604800
        
        return timedelta(seconds=total_seconds) if total_seconds > 0 else None
    
    @staticmethod
    def format_timedelta(td: timedelta) -> str:
        """Format timedelta into human readable string"""
        total_seconds = int(td.total_seconds())
        
        days = total_seconds // 86400
        hours = (total_seconds % 86400) // 3600
        minutes = (total_seconds % 3600) // 60
        seconds = total_seconds % 60
        
        parts = []
        if days:
            parts.append(f"{days} day{'s' if days != 1 else ''}")
        if hours:
            parts.append(f"{hours} hour{'s' if hours != 1 else ''}")
        if minutes:
            parts.append(f"{minutes} minute{'s' if minutes != 1 else ''}")
        if seconds and not (days or hours):
            parts.append(f"{seconds} second{'s' if seconds != 1 else ''}")
        
        return ", ".join(parts) if parts else "0 seconds"

class PaginationView(discord.ui.View):
    """Pagination view for long lists"""
    
    def __init__(self, embeds: List[discord.Embed], timeout: int = 300):
        super().__init__(timeout=timeout)
        self.embeds = embeds
        self.current_page = 0
        self.max_pages = len(embeds)
        
        # Update button states
        self.update_buttons()
    
    def update_buttons(self):
        """Update button states based on current page"""
        self.previous_button.disabled = self.current_page == 0
        self.next_button.disabled = self.current_page == self.max_pages - 1
    
    @discord.ui.button(label='◀️', style=discord.ButtonStyle.secondary)
    async def previous_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Go to previous page"""
        if self.current_page > 0:
            self.current_page -= 1
            self.update_buttons()
            await interaction.response.edit_message(embed=self.embeds[self.current_page], view=self)
    
    @discord.ui.button(label='▶️', style=discord.ButtonStyle.secondary)
    async def next_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Go to next page"""
        if self.current_page < self.max_pages - 1:
            self.current_page += 1
            self.update_buttons()
            await interaction.response.edit_message(embed=self.embeds[self.current_page], view=self)
    
    @discord.ui.button(label='🗑️', style=discord.ButtonStyle.danger)
    async def delete_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Delete the message"""
        await interaction.response.defer()
        await interaction.delete_original_response()

async def safe_send(destination: Union[discord.TextChannel, discord.User], 
                   content: str = None, embed: discord.Embed = None, 
                   view: discord.ui.View = None) -> Optional[discord.Message]:
    """Safely send a message with error handling"""
    try:
        return await destination.send(content=content, embed=embed, view=view)
    except discord.Forbidden:
        print(f"Missing permissions to send message to {destination}")
    except discord.HTTPException as e:
        print(f"HTTP error sending message: {e}")
    except Exception as e:
        print(f"Unexpected error sending message: {e}")
    return None

async def safe_edit(message: discord.Message, content: str = None, 
                   embed: discord.Embed = None, view: discord.ui.View = None) -> bool:
    """Safely edit a message with error handling"""
    try:
        await message.edit(content=content, embed=embed, view=view)
        return True
    except discord.NotFound:
        print("Message not found for editing")
    except discord.Forbidden:
        print("Missing permissions to edit message")
    except discord.HTTPException as e:
        print(f"HTTP error editing message: {e}")
    except Exception as e:
        print(f"Unexpected error editing message: {e}")
    return False

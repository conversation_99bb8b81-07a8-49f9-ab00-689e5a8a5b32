#!/usr/bin/env python3
"""
ChatterBot Setup Script
Automated setup for ChatterBot ModMail bot
"""

import os
import sys
import json
import asyncio
import discord
from discord.ext import commands

def create_env_file():
    """Create .env file from template"""
    if os.path.exists('.env'):
        print("✅ .env file already exists")
        return
    
    if os.path.exists('.env.example'):
        with open('.env.example', 'r') as f:
            content = f.read()
        
        with open('.env', 'w') as f:
            f.write(content)
        
        print("✅ Created .env file from template")
        print("📝 Please edit .env with your bot token and configuration")
    else:
        print("❌ .env.example not found")

def create_data_directory():
    """Create data directory for database and config"""
    os.makedirs('data', exist_ok=True)
    print("✅ Created data directory")

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required!")
        print(f"Current version: {sys.version}")
        return False
    
    print(f"✅ Python version: {sys.version}")
    return True

def install_dependencies():
    """Install required dependencies"""
    try:
        import subprocess
        result = subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Dependencies installed successfully")
        else:
            print("❌ Failed to install dependencies")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Error installing dependencies: {e}")
        return False
    
    return True

def validate_bot_token():
    """Validate Discord bot token"""
    from dotenv import load_dotenv
    load_dotenv()
    
    token = os.getenv('DISCORD_TOKEN')
    if not token:
        print("❌ DISCORD_TOKEN not found in .env file")
        return False
    
    if not token.startswith(('Bot ', 'MTk', 'MTA', 'MTI')):
        print("⚠️ Bot token format looks incorrect")
        return False
    
    print("✅ Bot token found")
    return True

async def test_bot_connection():
    """Test bot connection to Discord"""
    from dotenv import load_dotenv
    load_dotenv()
    
    token = os.getenv('DISCORD_TOKEN')
    if not token:
        print("❌ No bot token to test")
        return False
    
    try:
        intents = discord.Intents.default()
        intents.message_content = True
        
        bot = commands.Bot(command_prefix='!', intents=intents)
        
        @bot.event
        async def on_ready():
            print(f"✅ Bot connected as {bot.user}")
            await bot.close()
        
        await bot.start(token)
        return True
        
    except discord.LoginFailure:
        print("❌ Invalid bot token")
        return False
    except Exception as e:
        print(f"❌ Connection test failed: {e}")
        return False

def create_systemd_service():
    """Create systemd service file"""
    service_content = f"""[Unit]
Description=ChatterBot ModMail
After=network.target

[Service]
Type=simple
User={os.getenv('USER', 'chatterbot')}
WorkingDirectory={os.getcwd()}
ExecStart={sys.executable} main.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
"""
    
    with open('chatterbot.service', 'w') as f:
        f.write(service_content)
    
    print("✅ Created systemd service file: chatterbot.service")
    print("📝 To install: sudo cp chatterbot.service /etc/systemd/system/")
    print("📝 To enable: sudo systemctl enable chatterbot")
    print("📝 To start: sudo systemctl start chatterbot")

def create_docker_files():
    """Create Docker configuration files"""
    dockerfile_content = """FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    gcc \\
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create data directory
RUN mkdir -p data

# Run the bot
CMD ["python", "main.py"]
"""
    
    docker_compose_content = """version: '3.8'

services:
  chatterbot:
    build: .
    container_name: chatterbot
    restart: unless-stopped
    volumes:
      - ./data:/app/data
      - ./.env:/app/.env
    environment:
      - PYTHONUNBUFFERED=1
"""
    
    with open('Dockerfile', 'w') as f:
        f.write(dockerfile_content)
    
    with open('docker-compose.yml', 'w') as f:
        f.write(docker_compose_content)
    
    print("✅ Created Docker files: Dockerfile, docker-compose.yml")
    print("📝 To build: docker-compose build")
    print("📝 To run: docker-compose up -d")

def main():
    """Main setup function"""
    print("🤖 ChatterBot Setup Script")
    print("=" * 40)
    
    # Check Python version
    if not check_python_version():
        return
    
    # Create necessary directories and files
    create_data_directory()
    create_env_file()
    
    # Install dependencies
    print("\n📦 Installing dependencies...")
    if not install_dependencies():
        print("❌ Setup failed at dependency installation")
        return
    
    # Validate configuration
    print("\n🔧 Validating configuration...")
    if not validate_bot_token():
        print("⚠️ Please configure your bot token in .env file")
    
    # Create deployment files
    print("\n🚀 Creating deployment files...")
    create_systemd_service()
    create_docker_files()
    
    print("\n✅ Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Edit .env file with your bot token and configuration")
    print("2. Create a Discord application and bot at https://discord.com/developers/applications")
    print("3. Invite the bot to your server with appropriate permissions")
    print("4. Run: python main.py")
    print("\n📚 For detailed setup instructions, see README.md")

if __name__ == "__main__":
    main()

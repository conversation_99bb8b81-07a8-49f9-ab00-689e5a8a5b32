const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');
const database = require('../database/database');
const config = require('../config/config');
const { Em<PERSON><PERSON><PERSON><PERSON>, <PERSON>mpo<PERSON><PERSON><PERSON><PERSON>, TimeParser, safeReply } = require('../utils/helpers');
const { requireAdmin, requireModerator, PermissionManager } = require('../utils/permissions');
const TaskScheduler = require('../utils/scheduler');

class AdminCommands {
    constructor(client) {
        this.client = client;
        this.scheduler = new TaskScheduler(client);
    }

    async blacklistUser(interaction) {
        const user = interaction.options.getUser('user');
        const reason = interaction.options.getString('reason') || 'No reason provided';

        const success = await database.blacklistUser(user.id, reason, interaction.user.id);

        if (success) {
            const embed = EmbedHelper.success(
                'User Blacklisted',
                `${user} has been blacklisted from creating tickets.`
            ).addFields({ name: 'Reason', value: reason, inline: false });

            // Close any open tickets for this user
            const openTickets = await database.getUserTickets(user.id, 'open');
            for (const ticket of openTickets) {
                const channel = this.client.channels.cache.get(ticket.channel_id);
                if (channel) {
                    await database.closeTicket(
                        channel.id,
                        interaction.user.id,
                        `User blacklisted: ${reason}`
                    );

                    const embedClose = EmbedHelper.warning(
                        'Ticket Closed',
                        'This ticket has been closed because the user was blacklisted.'
                    );
                    await channel.send({ embeds: [embedClose] });

                    // Schedule channel deletion
                    setTimeout(async () => {
                        try {
                            await channel.delete('User blacklisted');
                        } catch (error) {
                            console.error('Error deleting blacklisted user channel:', error);
                        }
                    }, 30000);
                }
            }

            await safeReply(interaction, { embeds: [embed] });
        } else {
            const embed = EmbedHelper.error(
                'Blacklist Failed',
                'Failed to blacklist user. They may already be blacklisted.'
            );
            await safeReply(interaction, { embeds: [embed], ephemeral: true });
        }
    }

    async unblacklistUser(interaction) {
        const user = interaction.options.getUser('user');
        const success = await database.unblacklistUser(user.id);

        if (success) {
            const embed = EmbedHelper.success(
                'User Unblacklisted',
                `${user} has been removed from the blacklist.`
            );
            await safeReply(interaction, { embeds: [embed] });
        } else {
            const embed = EmbedHelper.error(
                'User Not Blacklisted',
                `${user} is not currently blacklisted.`
            );
            await safeReply(interaction, { embeds: [embed], ephemeral: true });
        }
    }

    async checkBlacklist(interaction) {
        const user = interaction.options.getUser('user');
        const isBlacklisted = await database.isBlacklisted(user.id);

        if (isBlacklisted) {
            const embed = EmbedHelper.warning(
                'User Blacklisted',
                `${user} is currently blacklisted.`
            );
            await safeReply(interaction, { embeds: [embed], ephemeral: true });
        } else {
            const embed = EmbedHelper.success(
                'User Not Blacklisted',
                `${user} is not blacklisted.`
            );
            await safeReply(interaction, { embeds: [embed], ephemeral: true });
        }
    }

    async listBlacklist(interaction) {
        try {
            const blacklistedUsers = await database.all(
                'SELECT user_id, blacklist_reason, blacklisted_at FROM users WHERE is_blacklisted = TRUE'
            );

            if (blacklistedUsers.length === 0) {
                const embed = EmbedHelper.info('Blacklist Empty', 'No users are currently blacklisted.');
                await safeReply(interaction, { embeds: [embed], ephemeral: true });
                return;
            }

            const embed = EmbedHelper.info('Blacklisted Users', '');
            
            const userList = blacklistedUsers.map(user => {
                const userTag = this.client.users.cache.get(user.user_id)?.tag || `Unknown User (${user.user_id})`;
                const date = new Date(user.blacklisted_at).toLocaleDateString();
                return `**${userTag}**\nReason: ${user.blacklist_reason}\nDate: ${date}`;
            });

            embed.setDescription(userList.join('\n\n'));
            embed.setFooter({ text: `Total: ${blacklistedUsers.length} blacklisted users` });

            await safeReply(interaction, { embeds: [embed], ephemeral: true });
        } catch (error) {
            console.error('Error listing blacklist:', error);
            const embed = EmbedHelper.error('Error', 'Failed to retrieve blacklist.');
            await safeReply(interaction, { embeds: [embed], ephemeral: true });
        }
    }

    async closeAllTickets(interaction) {
        const reason = interaction.options.getString('reason') || 'Mass closure by admin';

        // Get all open tickets
        try {
            const openTickets = await database.all('SELECT * FROM tickets WHERE status = "open"');

            if (openTickets.length === 0) {
                const embed = EmbedHelper.info('No Open Tickets', 'There are no open tickets to close.');
                await safeReply(interaction, { embeds: [embed], ephemeral: true });
                return;
            }

            const embed = EmbedHelper.warning(
                'Confirm Mass Closure',
                `Are you sure you want to close ALL ${openTickets.length} open tickets?\n**Reason:** ${reason}`
            );

            const buttons = ComponentHelper.createConfirmationButtons('closeall');
            await safeReply(interaction, { embeds: [embed], components: [buttons], ephemeral: true });

            // Store the reason for the confirmation handler
            this.client.pendingCloseAll = this.client.pendingCloseAll || new Map();
            this.client.pendingCloseAll.set(interaction.user.id, { reason, tickets: openTickets });

        } catch (error) {
            console.error('Error getting open tickets:', error);
            const embed = EmbedHelper.error('Error', 'Failed to retrieve open tickets.');
            await safeReply(interaction, { embeds: [embed], ephemeral: true });
        }
    }

    async handleCloseAllConfirmation(interaction) {
        const [action] = interaction.customId.split('_');
        const pendingData = this.client.pendingCloseAll?.get(interaction.user.id);

        if (!pendingData) {
            await safeReply(interaction, { content: '❌ Close all operation not found or expired.', ephemeral: true });
            return;
        }

        if (action === 'confirm') {
            await interaction.deferUpdate();

            let closedCount = 0;
            for (const ticket of pendingData.tickets) {
                try {
                    const channel = this.client.channels.cache.get(ticket.channel_id);
                    if (channel) {
                        await database.closeTicket(channel.id, interaction.user.id, pendingData.reason);
                        
                        const embed = EmbedHelper.warning(
                            'Ticket Closed',
                            `This ticket has been closed by an administrator.\n**Reason:** ${pendingData.reason}`
                        );
                        await channel.send({ embeds: [embed] });

                        // Schedule channel deletion
                        setTimeout(async () => {
                            try {
                                await channel.delete('Mass closure by admin');
                            } catch (error) {
                                console.error('Error deleting channel in mass closure:', error);
                            }
                        }, 10000);

                        closedCount++;
                    }
                } catch (error) {
                    console.error('Error closing ticket in mass closure:', error);
                }
            }

            const embed = EmbedHelper.success(
                'All Tickets Closed',
                `Successfully closed ${closedCount} tickets.\n**Reason:** ${pendingData.reason}`
            );
            await interaction.editReply({ embeds: [embed], components: [] });
        } else {
            const embed = EmbedHelper.info('Operation Cancelled', 'Mass ticket closure was cancelled.');
            await interaction.update({ embeds: [embed], components: [] });
        }

        // Clean up pending data
        this.client.pendingCloseAll?.delete(interaction.user.id);
    }

    async anonymousReply(interaction) {
        const message = interaction.options.getString('message');
        
        const ticket = await database.getTicket(interaction.channel.id);
        if (!ticket) {
            const embed = EmbedHelper.error('Not a Ticket', 'This command can only be used in ticket channels.');
            await safeReply(interaction, { embeds: [embed], ephemeral: true });
            return;
        }

        // Get user
        const user = this.client.users.cache.get(ticket.user_id);
        if (!user) {
            const embed = EmbedHelper.error('User Not Found', 'Could not find the ticket user.');
            await safeReply(interaction, { embeds: [embed], ephemeral: true });
            return;
        }

        // Send anonymous message to user
        const embed = new EmbedBuilder()
            .setDescription(message)
            .setColor(0x00ff00)
            .setAuthor({ 
                name: 'Support Team', 
                iconURL: interaction.guild.iconURL() || undefined 
            })
            .setTimestamp();

        try {
            await user.send({ embeds: [embed] });

            // Log the anonymous message
            await database.logMessage(
                ticket.id, 
                0, 
                interaction.user.id, 
                message, 
                null, 
                true, // isStaff
                true  // isAnonymous
            );

            // Confirm in channel
            const confirmEmbed = EmbedHelper.success('Anonymous Reply Sent', 'Your message has been sent anonymously.');
            await safeReply(interaction, { embeds: [confirmEmbed], ephemeral: true });

        } catch (error) {
            console.error('Error sending anonymous reply:', error);
            const embed = EmbedHelper.error('Cannot Send DM', 'Unable to send DM to user.');
            await safeReply(interaction, { embeds: [embed], ephemeral: true });
        }
    }

    async scheduleClose(interaction) {
        const timeStr = interaction.options.getString('time');
        const reason = interaction.options.getString('reason') || 'Scheduled closure';

        const ticket = await database.getTicket(interaction.channel.id);
        if (!ticket) {
            const embed = EmbedHelper.error('Not a Ticket', 'This command can only be used in ticket channels.');
            await safeReply(interaction, { embeds: [embed], ephemeral: true });
            return;
        }

        // Parse time
        const scheduledTime = TimeParser.parseTime(timeStr);
        if (!scheduledTime) {
            const embed = EmbedHelper.error(
                'Invalid Time Format',
                'Please use format like: `1h`, `30m`, `2d`, etc.'
            );
            await safeReply(interaction, { embeds: [embed], ephemeral: true });
            return;
        }

        // Schedule the closure
        await this.scheduler.scheduleTicketClose(interaction.channel.id, scheduledTime.getTime() - Date.now(), reason);

        // Confirm scheduling
        const timeUntil = scheduledTime.getTime() - Date.now();
        const timeStr2 = TimeParser.formatDuration(timeUntil);
        const embed = EmbedHelper.success(
            'Ticket Scheduled for Closure',
            `This ticket will be closed in ${timeStr2}.\n**Reason:** ${reason}`
        );
        await safeReply(interaction, { embeds: [embed] });
    }

    async showStats(interaction) {
        try {
            // Get various statistics
            const totalTickets = await database.get('SELECT COUNT(*) as count FROM tickets');
            const openTickets = await database.get('SELECT COUNT(*) as count FROM tickets WHERE status = "open"');
            const closedToday = await database.get(`
                SELECT COUNT(*) as count FROM tickets 
                WHERE status = "closed" AND DATE(closed_at) = DATE('now')
            `);
            const totalUsers = await database.get('SELECT COUNT(DISTINCT user_id) as count FROM tickets');
            const blacklistedUsers = await database.get('SELECT COUNT(*) as count FROM users WHERE is_blacklisted = TRUE');
            const totalSnippets = await database.get('SELECT COUNT(*) as count FROM snippets');

            const embed = EmbedHelper.info('ChatterBot Statistics', '')
                .addFields(
                    { name: 'Total Tickets', value: totalTickets.count.toString(), inline: true },
                    { name: 'Open Tickets', value: openTickets.count.toString(), inline: true },
                    { name: 'Closed Today', value: closedToday.count.toString(), inline: true },
                    { name: 'Total Users', value: totalUsers.count.toString(), inline: true },
                    { name: 'Blacklisted Users', value: blacklistedUsers.count.toString(), inline: true },
                    { name: 'Total Snippets', value: totalSnippets.count.toString(), inline: true }
                )
                .setFooter({ text: `Bot uptime: ${this.getUptime()}` });

            await safeReply(interaction, { embeds: [embed], ephemeral: true });
        } catch (error) {
            console.error('Error getting stats:', error);
            const embed = EmbedHelper.error('Error', 'Failed to retrieve statistics.');
            await safeReply(interaction, { embeds: [embed], ephemeral: true });
        }
    }

    getUptime() {
        const uptime = process.uptime();
        const days = Math.floor(uptime / 86400);
        const hours = Math.floor((uptime % 86400) / 3600);
        const minutes = Math.floor((uptime % 3600) / 60);
        
        return `${days}d ${hours}h ${minutes}m`;
    }
}

// Slash command definitions
const commands = [
    new SlashCommandBuilder()
        .setName('blacklist')
        .setDescription('Manage user blacklist')
        .addSubcommand(subcommand =>
            subcommand
                .setName('add')
                .setDescription('Blacklist a user from creating tickets')
                .addUserOption(option =>
                    option.setName('user')
                        .setDescription('User to blacklist')
                        .setRequired(true)
                )
                .addStringOption(option =>
                    option.setName('reason')
                        .setDescription('Reason for blacklisting')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('remove')
                .setDescription('Remove a user from the blacklist')
                .addUserOption(option =>
                    option.setName('user')
                        .setDescription('User to unblacklist')
                        .setRequired(true)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('check')
                .setDescription('Check if a user is blacklisted')
                .addUserOption(option =>
                    option.setName('user')
                        .setDescription('User to check')
                        .setRequired(true)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('list')
                .setDescription('List all blacklisted users')
        ),
    
    new SlashCommandBuilder()
        .setName('closeall')
        .setDescription('Close all open tickets')
        .addStringOption(option =>
            option.setName('reason')
                .setDescription('Reason for closing all tickets')
                .setRequired(false)
        ),
    
    new SlashCommandBuilder()
        .setName('schedule')
        .setDescription('Schedule a ticket to close after specified time')
        .addStringOption(option =>
            option.setName('time')
                .setDescription('Time until closure (e.g., 1h, 30m, 2d)')
                .setRequired(true)
        )
        .addStringOption(option =>
            option.setName('reason')
                .setDescription('Reason for closure')
                .setRequired(false)
        ),
    
    new SlashCommandBuilder()
        .setName('stats')
        .setDescription('Show bot statistics')
];

module.exports = {
    AdminCommands,
    commands
};

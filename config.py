import os
import json
from typing import Dict, Any, Optional
from dotenv import load_dotenv

load_dotenv()

class Config:
    """Configuration management for ChatterBot"""
    
    def __init__(self):
        self.config_file = "data/config.json"
        self.default_config = {
            "bot_settings": {
                "prefix": os.getenv("PREFIX", "!"),
                "status": "DM me for support!",
                "activity_type": "watching"
            },
            "modmail": {
                "category_id": int(os.getenv("MODMAIL_CATEGORY_ID", 0)),
                "log_channel_id": int(os.getenv("LOG_CHANNEL_ID", 0)),
                "auto_close_hours": int(os.getenv("AUTO_CLOSE_INACTIVE_HOURS", 72)),
                "max_tickets_per_user": int(os.getenv("MAX_TICKETS_PER_USER", 3)),
                "enable_anonymous_staff": True,
                "enable_user_typing": True,
                "enable_staff_typing": True
            },
            "permissions": {
                "staff_role_id": int(os.getenv("STAFF_ROLE_ID", 0)),
                "admin_role_id": int(os.getenv("ADMIN_ROLE_ID", 0)),
                "moderator_role_id": 0,
                "support_role_id": 0
            },
            "ai_features": {
                "enabled": os.getenv("ENABLE_AI_REPLIES", "false").lower() == "true",
                "openai_api_key": os.getenv("OPENAI_API_KEY", ""),
                "model": "gpt-3.5-turbo",
                "max_tokens": 500,
                "temperature": 0.7
            },
            "messages": {
                "welcome_message": "Hello {username}! Thank you for contacting our support team. A staff member will be with you shortly.",
                "close_message": "This ticket has been closed. If you need further assistance, feel free to send another message.",
                "anonymous_close_message": "This ticket has been closed by staff. If you need further assistance, feel free to send another message.",
                "blacklist_message": "You are currently blacklisted from creating support tickets.",
                "max_tickets_message": "You have reached the maximum number of open tickets ({max_tickets}). Please wait for your current tickets to be resolved."
            },
            "logging": {
                "log_all_messages": True,
                "log_staff_actions": True,
                "log_user_actions": True,
                "create_transcripts": True,
                "transcript_format": "html"
            }
        }
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """Load configuration from file or create default"""
        os.makedirs("data", exist_ok=True)
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                # Merge with defaults to ensure all keys exist
                return self._merge_configs(self.default_config, config)
            except (json.JSONDecodeError, FileNotFoundError):
                pass
        
        # Create default config file
        self.save_config(self.default_config)
        return self.default_config.copy()
    
    def save_config(self, config: Optional[Dict[str, Any]] = None) -> None:
        """Save configuration to file"""
        if config is None:
            config = self.config
        
        os.makedirs("data", exist_ok=True)
        with open(self.config_file, 'w') as f:
            json.dump(config, f, indent=4)
    
    def _merge_configs(self, default: Dict[str, Any], user: Dict[str, Any]) -> Dict[str, Any]:
        """Recursively merge user config with defaults"""
        result = default.copy()
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
        return result
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value using dot notation"""
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any) -> None:
        """Set configuration value using dot notation"""
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
        self.save_config()
    
    @property
    def discord_token(self) -> str:
        return os.getenv("DISCORD_TOKEN", "")
    
    @property
    def guild_id(self) -> int:
        return int(os.getenv("GUILD_ID", 0))
    
    @property
    def database_path(self) -> str:
        return os.getenv("DATABASE_PATH", "data/database.db")

# Global config instance
config = Config()

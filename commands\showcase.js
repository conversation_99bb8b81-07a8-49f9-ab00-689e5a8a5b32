const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const { EmbedHelper, safeReply } = require('../utils/helpers');

class ShowcaseManager {
    constructor(client) {
        this.client = client;
    }

    async showFeatures(interaction) {
        const embeds = [
            this.createOverviewEmbed(),
            this.createCoreEmbed(),
            this.createAdvancedEmbed(),
            this.createAIEmbed(),
            this.createAnalyticsEmbed(),
            this.createAutomationEmbed()
        ];

        const buttons = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('showcase_prev')
                    .setLabel('◀️ Previous')
                    .setStyle(ButtonStyle.Secondary)
                    .setDisabled(true),
                new ButtonBuilder()
                    .setCustomId('showcase_next')
                    .setLabel('Next ▶️')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('showcase_close')
                    .setLabel('Close')
                    .setStyle(ButtonStyle.Danger)
            );

        await safeReply(interaction, { 
            embeds: [embeds[0]], 
            components: [buttons],
            ephemeral: true 
        });

        // Store showcase data for pagination
        this.client.showcaseData = this.client.showcaseData || new Map();
        this.client.showcaseData.set(interaction.user.id, {
            embeds: embeds,
            currentPage: 0,
            messageId: null
        });
    }

    createOverviewEmbed() {
        return new EmbedBuilder()
            .setTitle('🤖 ChatterBot - Feature Showcase')
            .setDescription('Welcome to ChatterBot, the most advanced ModMail bot with AI-powered features!')
            .setColor(0x0099ff)
            .addFields(
                {
                    name: '🎯 What Makes ChatterBot Special?',
                    value: '• **AI-Powered Responses** using OpenRouter\n• **Advanced Analytics** with visual reports\n• **Smart Automation** with auto-responders\n• **Interactive Ticket System** with categories\n• **Comprehensive Admin Tools**\n• **Real-time Performance Monitoring**',
                    inline: false
                },
                {
                    name: '📊 Quick Stats',
                    value: `• **${this.client.guilds.cache.size}** Servers\n• **${this.client.users.cache.size}** Users\n• **${this.client.commands.size}** Commands\n• **${Math.floor(process.uptime() / 3600)}h** Uptime`,
                    inline: true
                },
                {
                    name: '🚀 Getting Started',
                    value: 'Use `/ticket panel` to create a ticket system\nUse `/help` to see all commands\nUse `/automation setup` for workflows',
                    inline: true
                }
            )
            .setFooter({ text: 'Page 1/6 - Use buttons to navigate' })
            .setTimestamp();
    }

    createCoreEmbed() {
        return new EmbedBuilder()
            .setTitle('🎫 Core ModMail Features')
            .setDescription('Essential ticket management capabilities')
            .setColor(0x00ff00)
            .addFields(
                {
                    name: '📋 Ticket Management',
                    value: '• **Multi-Category System** - Organize by type\n• **Interactive Panels** - Easy ticket creation\n• **Priority Levels** - Urgent, High, Normal, Low\n• **Staff Assignment** - Claim and assign tickets\n• **Transfer System** - Move between categories',
                    inline: false
                },
                {
                    name: '💬 Communication',
                    value: '• **Real-time Forwarding** - Seamless chat\n• **Anonymous Replies** - Staff privacy\n• **Rich Embeds** - Beautiful formatting\n• **Attachment Support** - Files and images\n• **Typing Indicators** - Live feedback',
                    inline: false
                },
                {
                    name: '⚙️ Administration',
                    value: '• **User Blacklisting** - Block problematic users\n• **Bulk Operations** - Close multiple tickets\n• **Scheduled Actions** - Auto-close timers\n• **Permission System** - Role-based access\n• **Audit Logging** - Track all actions',
                    inline: false
                }
            )
            .setFooter({ text: 'Page 2/6 - Core Features' })
            .setTimestamp();
    }

    createAdvancedEmbed() {
        return new EmbedBuilder()
            .setTitle('⚡ Advanced Features')
            .setDescription('Professional-grade capabilities for enterprise use')
            .setColor(0xff9900)
            .addFields(
                {
                    name: '📝 Snippet System',
                    value: '• **Canned Responses** - Quick replies\n• **Category Organization** - Sorted by type\n• **Usage Analytics** - Track popularity\n• **Search Function** - Find snippets fast\n• **Variable Support** - Dynamic content',
                    inline: false
                },
                {
                    name: '🔒 Security & Privacy',
                    value: '• **Anonymous Staff Mode** - Hide identities\n• **Secure Logging** - Encrypted storage\n• **Permission Levels** - Granular control\n• **Audit Trails** - Complete history\n• **Data Protection** - GDPR compliant',
                    inline: false
                },
                {
                    name: '⏰ Scheduling & SLA',
                    value: '• **Auto-close Timers** - Inactive tickets\n• **Reminder System** - Follow-up alerts\n• **SLA Tracking** - Response time goals\n• **Business Hours** - Time-based rules\n• **Escalation Rules** - Priority handling',
                    inline: false
                }
            )
            .setFooter({ text: 'Page 3/6 - Advanced Features' })
            .setTimestamp();
    }

    createAIEmbed() {
        return new EmbedBuilder()
            .setTitle('🤖 AI-Powered Features')
            .setDescription('Cutting-edge AI integration using OpenRouter')
            .setColor(0x9932cc)
            .addFields(
                {
                    name: '💡 Smart Responses',
                    value: '• **Context-Aware AI** - Understands conversation\n• **Multiple Models** - Llama 3.2, GPT, Claude\n• **Custom Prompts** - Tailored responses\n• **Quality Control** - Review before sending\n• **Learning System** - Improves over time',
                    inline: false
                },
                {
                    name: '📊 AI Analytics',
                    value: '• **Conversation Summaries** - Auto-generated\n• **Sentiment Analysis** - User emotions\n• **Topic Classification** - Auto-categorization\n• **Response Quality** - Performance metrics\n• **Usage Statistics** - AI effectiveness',
                    inline: false
                },
                {
                    name: '🌐 Language Support',
                    value: '• **Real-time Translation** - 100+ languages\n• **Auto-detection** - Identify languages\n• **Cultural Adaptation** - Context-aware\n• **Multilingual Support** - Global teams\n• **Localization** - Regional preferences',
                    inline: false
                }
            )
            .setFooter({ text: 'Page 4/6 - AI Features' })
            .setTimestamp();
    }

    createAnalyticsEmbed() {
        return new EmbedBuilder()
            .setTitle('📊 Advanced Analytics')
            .setDescription('Comprehensive reporting and insights')
            .setColor(0xff4444)
            .addFields(
                {
                    name: '📈 Performance Metrics',
                    value: '• **Response Times** - Average resolution\n• **Ticket Volume** - Daily/weekly trends\n• **Staff Performance** - Individual stats\n• **Category Breakdown** - Popular topics\n• **User Satisfaction** - Rating system',
                    inline: false
                },
                {
                    name: '🔥 Visual Reports',
                    value: '• **Heatmaps** - Peak activity times\n• **Trend Charts** - Historical data\n• **CSV Exports** - Data portability\n• **Real-time Dashboards** - Live updates\n• **Custom Reports** - Tailored insights',
                    inline: false
                },
                {
                    name: '🎯 Business Intelligence',
                    value: '• **SLA Compliance** - Goal tracking\n• **Resource Planning** - Staff allocation\n• **Capacity Analysis** - Load balancing\n• **Predictive Analytics** - Future trends\n• **ROI Metrics** - Value measurement',
                    inline: false
                }
            )
            .setFooter({ text: 'Page 5/6 - Analytics' })
            .setTimestamp();
    }

    createAutomationEmbed() {
        return new EmbedBuilder()
            .setTitle('🔄 Smart Automation')
            .setDescription('Intelligent workflows and auto-responses')
            .setColor(0x00ffff)
            .addFields(
                {
                    name: '🤖 Auto-Responders',
                    value: '• **Keyword Triggers** - Smart detection\n• **Conditional Logic** - Complex rules\n• **Time-based Rules** - Business hours\n• **User Segmentation** - Targeted responses\n• **A/B Testing** - Optimize effectiveness',
                    inline: false
                },
                {
                    name: '⚡ Workflow Automation',
                    value: '• **Ticket Routing** - Auto-assignment\n• **Escalation Rules** - Priority handling\n• **Follow-up Sequences** - Automated care\n• **Integration Hooks** - External systems\n• **Custom Triggers** - Flexible conditions',
                    inline: false
                },
                {
                    name: '📋 Process Optimization',
                    value: '• **Queue Management** - Load balancing\n• **Smart Categorization** - AI-powered\n• **Duplicate Detection** - Merge similar\n• **Satisfaction Surveys** - Auto-feedback\n• **Performance Monitoring** - Real-time alerts',
                    inline: false
                }
            )
            .setFooter({ text: 'Page 6/6 - Automation' })
            .setTimestamp();
    }

    async handleShowcaseNavigation(interaction) {
        const userId = interaction.user.id;
        const showcaseData = this.client.showcaseData?.get(userId);
        
        if (!showcaseData) {
            await interaction.reply({ content: '❌ Showcase session expired.', ephemeral: true });
            return;
        }

        const { embeds } = showcaseData;
        let { currentPage } = showcaseData;

        if (interaction.customId === 'showcase_next' && currentPage < embeds.length - 1) {
            currentPage++;
        } else if (interaction.customId === 'showcase_prev' && currentPage > 0) {
            currentPage--;
        } else if (interaction.customId === 'showcase_close') {
            this.client.showcaseData.delete(userId);
            await interaction.update({ content: '✅ Showcase closed.', embeds: [], components: [] });
            return;
        }

        // Update buttons
        const buttons = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('showcase_prev')
                    .setLabel('◀️ Previous')
                    .setStyle(ButtonStyle.Secondary)
                    .setDisabled(currentPage === 0),
                new ButtonBuilder()
                    .setCustomId('showcase_next')
                    .setLabel('Next ▶️')
                    .setStyle(ButtonStyle.Primary)
                    .setDisabled(currentPage === embeds.length - 1),
                new ButtonBuilder()
                    .setCustomId('showcase_close')
                    .setLabel('Close')
                    .setStyle(ButtonStyle.Danger)
            );

        showcaseData.currentPage = currentPage;
        await interaction.update({ embeds: [embeds[currentPage]], components: [buttons] });
    }

    async showQuickStart(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('🚀 ChatterBot Quick Start Guide')
            .setDescription('Get up and running in minutes!')
            .setColor(0x00ff00)
            .addFields(
                {
                    name: '1️⃣ Basic Setup',
                    value: '• Configure your `.env` file with tokens\n• Set up ModMail category and log channel\n• Configure staff and admin roles\n• Run `/ticket panel` to create ticket system',
                    inline: false
                },
                {
                    name: '2️⃣ AI Configuration',
                    value: '• Get OpenRouter API key (free tier available)\n• Enable AI features in config\n• Test with `/aireply` command\n• Set up auto-responders with `/automation create`',
                    inline: false
                },
                {
                    name: '3️⃣ Advanced Features',
                    value: '• Create snippets with `/snippets create`\n• Set up analytics with `/analytics overview`\n• Configure blacklist rules\n• Schedule automated tasks',
                    inline: false
                },
                {
                    name: '4️⃣ Monitoring',
                    value: '• Check `/stats` for performance\n• Use `/analytics heatmap` for insights\n• Monitor with `/automation list`\n• Export data with analytics reports',
                    inline: false
                }
            )
            .setFooter({ text: 'Need help? Check the documentation or ask in our support server!' })
            .setTimestamp();

        await safeReply(interaction, { embeds: [embed], ephemeral: true });
    }
}

// Slash command definitions
const commands = [
    new SlashCommandBuilder()
        .setName('showcase')
        .setDescription('Explore ChatterBot features')
        .addSubcommand(subcommand =>
            subcommand
                .setName('features')
                .setDescription('Interactive feature showcase')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('quickstart')
                .setDescription('Quick start guide')
        )
];

module.exports = {
    ShowcaseManager,
    commands
};

const fs = require('fs-extra');
const path = require('path');
require('dotenv').config();

class Config {
    constructor() {
        this.configFile = path.join(__dirname, '../data/config.json');
        this.defaultConfig = {
            bot_settings: {
                prefix: process.env.PREFIX || '!',
                status: 'DM me for support!',
                activity_type: 'WATCHING'
            },
            modmail: {
                category_id: process.env.MODMAIL_CATEGORY_ID || '',
                log_channel_id: process.env.LOG_CHANNEL_ID || '',
                auto_close_hours: parseInt(process.env.AUTO_CLOSE_INACTIVE_HOURS) || 72,
                max_tickets_per_user: parseInt(process.env.MAX_TICKETS_PER_USER) || 3,
                enable_anonymous_staff: true,
                enable_user_typing: true,
                enable_staff_typing: true
            },
            permissions: {
                staff_role_id: process.env.STAFF_ROLE_ID || '',
                admin_role_id: process.env.ADMIN_ROLE_ID || '',
                moderator_role_id: '',
                support_role_id: ''
            },
            ai_features: {
                enabled: process.env.ENABLE_AI_REPLIES === 'true',
                puter_api_key: process.env.PUTER_API_KEY || '',
                puter_app_id: process.env.PUTER_APP_ID || '',
                max_tokens: 500,
                temperature: 0.7
            },
            messages: {
                welcome_message: 'Hello {username}! Thank you for contacting our support team. A staff member will be with you shortly.',
                close_message: 'This ticket has been closed. If you need further assistance, feel free to send another message.',
                anonymous_close_message: 'This ticket has been closed by staff. If you need further assistance, feel free to send another message.',
                blacklist_message: 'You are currently blacklisted from creating support tickets.',
                max_tickets_message: 'You have reached the maximum number of open tickets ({max_tickets}). Please wait for your current tickets to be resolved.'
            },
            logging: {
                log_all_messages: true,
                log_staff_actions: true,
                log_user_actions: true,
                create_transcripts: true,
                transcript_format: 'html'
            }
        };
        
        this.config = this.loadConfig();
    }

    loadConfig() {
        try {
            // Ensure data directory exists
            fs.ensureDirSync(path.dirname(this.configFile));
            
            if (fs.existsSync(this.configFile)) {
                const config = fs.readJsonSync(this.configFile);
                return this.mergeConfigs(this.defaultConfig, config);
            }
        } catch (error) {
            console.error('Error loading config:', error);
        }
        
        // Create default config file
        this.saveConfig(this.defaultConfig);
        return { ...this.defaultConfig };
    }

    saveConfig(config = null) {
        try {
            const configToSave = config || this.config;
            fs.ensureDirSync(path.dirname(this.configFile));
            fs.writeJsonSync(this.configFile, configToSave, { spaces: 4 });
        } catch (error) {
            console.error('Error saving config:', error);
        }
    }

    mergeConfigs(defaultConfig, userConfig) {
        const result = { ...defaultConfig };
        
        for (const [key, value] of Object.entries(userConfig)) {
            if (result[key] && typeof result[key] === 'object' && typeof value === 'object') {
                result[key] = { ...result[key], ...value };
            } else {
                result[key] = value;
            }
        }
        
        return result;
    }

    get(key, defaultValue = null) {
        const keys = key.split('.');
        let value = this.config;
        
        for (const k of keys) {
            if (value && typeof value === 'object' && k in value) {
                value = value[k];
            } else {
                return defaultValue;
            }
        }
        
        return value;
    }

    set(key, value) {
        const keys = key.split('.');
        let config = this.config;
        
        for (let i = 0; i < keys.length - 1; i++) {
            const k = keys[i];
            if (!config[k]) {
                config[k] = {};
            }
            config = config[k];
        }
        
        config[keys[keys.length - 1]] = value;
        this.saveConfig();
    }

    get discordToken() {
        return process.env.DISCORD_TOKEN;
    }

    get guildId() {
        return process.env.GUILD_ID;
    }

    get databasePath() {
        return process.env.DATABASE_PATH || './data/database.db';
    }

    get puterApiKey() {
        return process.env.PUTER_API_KEY;
    }

    get puterAppId() {
        return process.env.PUTER_APP_ID;
    }
}

module.exports = new Config();

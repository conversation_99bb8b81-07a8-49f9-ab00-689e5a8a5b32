import discord
from discord.ext import commands
import asyncio
import os
import sys
import traceback
from datetime import datetime

from config import config
from database import db
from utils.helpers import EmbedBuilder

class ChatterBot(commands.Bot):
    """Advanced ModMail Bot - ChatterBot"""
    
    def __init__(self):
        intents = discord.Intents.default()
        intents.message_content = True
        intents.members = True
        intents.guilds = True
        
        super().__init__(
            command_prefix=config.get('bot_settings.prefix', '!'),
            intents=intents,
            help_command=None,  # We'll create a custom help command
            case_insensitive=True,
            strip_after_prefix=True
        )
        
        self.start_time = datetime.now()
        self.version = "1.0.0"
    
    async def setup_hook(self):
        """Setup hook called when bot is starting"""
        print("🤖 ChatterBot is starting up...")
        
        # Connect to database
        await db.connect()
        print("✅ Database connected")
        
        # Load all cogs
        cogs = [
            'cogs.modmail',
            'cogs.snippets', 
            'cogs.admin',
            'cogs.ai_features'
        ]
        
        for cog in cogs:
            try:
                await self.load_extension(cog)
                print(f"✅ Loaded {cog}")
            except Exception as e:
                print(f"❌ Failed to load {cog}: {e}")
                traceback.print_exc()
        
        # Sync slash commands if in development
        if config.guild_id:
            guild = discord.Object(id=config.guild_id)
            self.tree.copy_global_to(guild=guild)
            await self.tree.sync(guild=guild)
            print(f"✅ Synced commands to guild {config.guild_id}")
    
    async def on_ready(self):
        """Called when bot is ready"""
        print(f"🎉 {self.user} is now online!")
        print(f"📊 Connected to {len(self.guilds)} guilds")
        print(f"👥 Serving {len(set(self.get_all_members()))} users")
        
        # Set bot status
        activity_type = config.get('bot_settings.activity_type', 'watching')
        status_text = config.get('bot_settings.status', 'DM me for support!')
        
        activity_types = {
            'playing': discord.ActivityType.playing,
            'watching': discord.ActivityType.watching,
            'listening': discord.ActivityType.listening,
            'streaming': discord.ActivityType.streaming
        }
        
        activity = discord.Activity(
            type=activity_types.get(activity_type, discord.ActivityType.watching),
            name=status_text
        )
        
        await self.change_presence(activity=activity, status=discord.Status.online)
        print(f"✅ Status set to: {activity_type} {status_text}")
    
    async def on_command_error(self, ctx, error):
        """Global error handler"""
        # Ignore command not found errors
        if isinstance(error, commands.CommandNotFound):
            return
        
        # Handle permission errors
        if isinstance(error, commands.CheckFailure):
            embed = EmbedBuilder.error(
                "Permission Denied",
                "You don't have permission to use this command."
            )
            await ctx.send(embed=embed, delete_after=10)
            return
        
        # Handle missing arguments
        if isinstance(error, commands.MissingRequiredArgument):
            embed = EmbedBuilder.error(
                "Missing Argument",
                f"Missing required argument: `{error.param.name}`"
            )
            await ctx.send(embed=embed, delete_after=10)
            return
        
        # Handle user input errors
        if isinstance(error, commands.UserInputError):
            embed = EmbedBuilder.error(
                "Invalid Input",
                str(error)
            )
            await ctx.send(embed=embed, delete_after=10)
            return
        
        # Handle cooldown errors
        if isinstance(error, commands.CommandOnCooldown):
            embed = EmbedBuilder.warning(
                "Command on Cooldown",
                f"Please wait {error.retry_after:.1f} seconds before using this command again."
            )
            await ctx.send(embed=embed, delete_after=10)
            return
        
        # Log unexpected errors
        print(f"Unexpected error in command {ctx.command}: {error}")
        traceback.print_exception(type(error), error, error.__traceback__)
        
        embed = EmbedBuilder.error(
            "Unexpected Error",
            "An unexpected error occurred. Please try again later."
        )
        await ctx.send(embed=embed, delete_after=10)
    
    async def on_guild_join(self, guild):
        """Called when bot joins a guild"""
        print(f"📥 Joined guild: {guild.name} ({guild.id})")
        
        # Send welcome message to system channel or first available channel
        channel = guild.system_channel
        if not channel:
            # Find first channel bot can send messages to
            for ch in guild.text_channels:
                if ch.permissions_for(guild.me).send_messages:
                    channel = ch
                    break
        
        if channel:
            embed = EmbedBuilder.info(
                "Thanks for adding ChatterBot!",
                "I'm an advanced ModMail bot that helps you manage support tickets efficiently."
            )
            embed.add_field(
                name="🚀 Getting Started",
                value="1. Create a category for tickets\n"
                      "2. Set up your configuration with `!config`\n"
                      "3. Configure staff roles\n"
                      "4. Users can now DM me to create tickets!",
                inline=False
            )
            embed.add_field(
                name="📚 Features",
                value="• Advanced ticket management\n"
                      "• Snippet system for quick replies\n"
                      "• AI-powered responses\n"
                      "• User blacklisting\n"
                      "• Scheduled ticket closing\n"
                      "• Anonymous staff replies",
                inline=False
            )
            embed.add_field(
                name="🔗 Support",
                value="Need help? Join our support server or check the documentation!",
                inline=False
            )
            
            try:
                await channel.send(embed=embed)
            except discord.Forbidden:
                pass
    
    async def on_guild_remove(self, guild):
        """Called when bot leaves a guild"""
        print(f"📤 Left guild: {guild.name} ({guild.id})")
    
    async def close(self):
        """Cleanup when bot shuts down"""
        print("🔄 ChatterBot is shutting down...")
        await db.close()
        await super().close()

# Custom help command
class HelpCommand(commands.HelpCommand):
    """Custom help command for ChatterBot"""
    
    def __init__(self):
        super().__init__(
            command_attrs={
                'help': 'Show help information for commands',
                'aliases': ['h']
            }
        )
    
    async def send_bot_help(self, mapping):
        """Send general help"""
        embed = EmbedBuilder.info("ChatterBot Help", "Advanced ModMail Bot")
        
        embed.add_field(
            name="🎫 Ticket Commands",
            value="`!close [reason]` - Close current ticket\n"
                  "`!aclose [reason]` - Close ticket anonymously\n"
                  "`!areply <message>` - Send anonymous reply\n"
                  "`!schedule <time> [reason]` - Schedule ticket closure",
            inline=False
        )
        
        embed.add_field(
            name="📝 Snippet Commands",
            value="`!snippet <name>` - Use a snippet\n"
                  "`!snippet list` - List all snippets\n"
                  "`!snippet create` - Create new snippet\n"
                  "`!snippet delete <name>` - Delete snippet",
            inline=False
        )
        
        embed.add_field(
            name="🤖 AI Commands",
            value="`!aireply [prompt]` - Generate AI response\n"
                  "`!summarize` - Summarize ticket conversation\n"
                  "`!translate <lang> <text>` - Translate text",
            inline=False
        )
        
        embed.add_field(
            name="⚙️ Admin Commands",
            value="`!blacklist add <user> [reason]` - Blacklist user\n"
                  "`!blacklist remove <user>` - Remove from blacklist\n"
                  "`!closeall [reason]` - Close all tickets\n"
                  "`!stats` - Show bot statistics",
            inline=False
        )
        
        embed.set_footer(text="Use !help <command> for detailed information about a specific command")
        
        await self.get_destination().send(embed=embed)
    
    async def send_command_help(self, command):
        """Send help for specific command"""
        embed = EmbedBuilder.info(f"Help: {command.name}", command.help or "No description available")
        
        if command.aliases:
            embed.add_field(name="Aliases", value=", ".join(command.aliases), inline=False)
        
        if command.signature:
            embed.add_field(name="Usage", value=f"`!{command.name} {command.signature}`", inline=False)
        
        await self.get_destination().send(embed=embed)

async def main():
    """Main function to run the bot"""
    # Check for required environment variables
    if not config.discord_token:
        print("❌ DISCORD_TOKEN not found in environment variables!")
        print("Please create a .env file with your bot token.")
        return
    
    if not config.guild_id:
        print("⚠️ GUILD_ID not set. Bot will work but slash commands won't sync.")
    
    # Create bot instance
    bot = ChatterBot()
    bot.help_command = HelpCommand()
    
    try:
        # Start the bot
        await bot.start(config.discord_token)
    except KeyboardInterrupt:
        print("\n🛑 Received keyboard interrupt, shutting down...")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        traceback.print_exc()
    finally:
        await bot.close()

if __name__ == "__main__":
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required!")
        sys.exit(1)
    
    # Run the bot
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 ChatterBot stopped.")
    except Exception as e:
        print(f"❌ Failed to start bot: {e}")
        traceback.print_exc()

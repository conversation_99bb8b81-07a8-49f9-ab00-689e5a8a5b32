# Discord Bot Configuration
DISCORD_TOKEN=your_discord_bot_token_here
GUILD_ID=your_server_id_here

# Puter.js Configuration (for AI features)
PUTER_API_KEY=your_puter_api_key_here
PUTER_APP_ID=your_puter_app_id_here

# Database Configuration
DATABASE_PATH=./data/database.db

# Bot Configuration
PREFIX=!
MODMAIL_CATEGORY_ID=your_modmail_category_id_here
LOG_CHANNEL_ID=your_log_channel_id_here
STAFF_ROLE_ID=your_staff_role_id_here
ADMIN_ROLE_ID=your_admin_role_id_here

# Advanced Features
ENABLE_AI_REPLIES=true
AUTO_CLOSE_INACTIVE_HOURS=72
MAX_TICKETS_PER_USER=3

# Development
NODE_ENV=production
DEBUG=false

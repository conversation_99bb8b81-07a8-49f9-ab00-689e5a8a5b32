# Discord Bot Configuration
DISCORD_TOKEN=your_discord_bot_token_here
GUILD_ID=your_server_id_here

# OpenRouter AI Configuration
OPENROUTER_API_KEY=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

# Database Configuration
DATABASE_PATH=./data/database.db

# Bot Configuration
PREFIX=!
MODMAIL_CATEGORY_ID=your_modmail_category_id_here
LOG_CHANNEL_ID=your_log_channel_id_here
STAFF_ROLE_ID=your_staff_role_id_here
ADMIN_ROLE_ID=your_admin_role_id_here

# Advanced Features
ENABLE_AI_REPLIES=true
AUTO_CLOSE_INACTIVE_HOURS=72
MAX_TICKETS_PER_USER=3

# Development
NODE_ENV=production
DEBUG=false

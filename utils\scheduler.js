const cron = require('node-cron');
const database = require('../database/database');
const config = require('../config/config');
const { EmbedHelper } = require('./helpers');

class TaskScheduler {
    constructor(client) {
        this.client = client;
        this.running = false;
        this.taskHandlers = {
            'close_ticket': this.handleCloseTicket.bind(this),
            'reminder': this.handleReminder.bind(this),
            'auto_response': this.handleAutoResponse.bind(this)
        };
    }

    start() {
        if (this.running) return;
        
        this.running = true;
        
        // Run every minute to check for due tasks
        cron.schedule('* * * * *', async () => {
            try {
                await this.processDueTasks();
                await this.checkInactiveTickets();
            } catch (error) {
                console.error('Error in scheduler:', error);
            }
        });

        console.log('✅ Task scheduler started');
    }

    stop() {
        this.running = false;
        console.log('🛑 Task scheduler stopped');
    }

    async processDueTasks() {
        const dueTasks = await database.getDueTasks();
        
        for (const task of dueTasks) {
            try {
                await this.executeTask(task);
                await database.completeTask(task.id);
            } catch (error) {
                console.error(`Error executing task ${task.id}:`, error);
            }
        }
    }

    async executeTask(task) {
        const handler = this.taskHandlers[task.task_type];
        
        if (handler) {
            await handler(task);
        } else {
            console.warn(`Unknown task type: ${task.task_type}`);
        }
    }

    async handleCloseTicket(task) {
        const channelId = task.target_id;
        const channel = this.client.channels.cache.get(channelId);
        
        if (!channel) return;

        // Get ticket info
        const ticket = await database.getTicket(channelId);
        if (!ticket) return;

        // Close the ticket
        await database.closeTicket(channelId, this.client.user.id, 'Auto-closed due to inactivity');

        // Send closing message
        const embed = EmbedHelper.warning(
            'Ticket Auto-Closed',
            'This ticket has been automatically closed due to inactivity.'
        );

        try {
            await channel.send({ embeds: [embed] });
            
            // Wait a bit then delete the channel
            setTimeout(async () => {
                try {
                    await channel.delete('Ticket auto-closed');
                } catch (error) {
                    console.error('Error deleting auto-closed channel:', error);
                }
            }, 10000);
            
        } catch (error) {
            console.error('Error sending auto-close message:', error);
        }
    }

    async handleReminder(task) {
        const data = task.data ? JSON.parse(task.data) : {};
        const channelId = data.channel_id;
        const message = data.message || 'Reminder!';
        
        if (!channelId) return;

        const channel = this.client.channels.cache.get(channelId);
        if (!channel) return;

        const embed = EmbedHelper.info('⏰ Reminder', message);

        try {
            await channel.send({ embeds: [embed] });
        } catch (error) {
            console.error('Error sending reminder:', error);
        }
    }

    async handleAutoResponse(task) {
        const data = task.data ? JSON.parse(task.data) : {};
        const channelId = data.channel_id;
        const response = data.response || '';
        
        if (!channelId || !response) return;

        const channel = this.client.channels.cache.get(channelId);
        if (!channel) return;

        try {
            await channel.send(response);
        } catch (error) {
            console.error('Error sending auto-response:', error);
        }
    }

    async checkInactiveTickets() {
        const autoCloseHours = config.get('modmail.auto_close_hours', 72);
        if (autoCloseHours <= 0) return;

        // This would require a more complex query to find inactive tickets
        // For now, we'll implement a basic version that could be expanded
        
        // Get all open tickets that haven't been active for the specified time
        const cutoffTime = new Date(Date.now() - (autoCloseHours * 60 * 60 * 1000));
        
        try {
            const inactiveTickets = await database.all(`
                SELECT * FROM tickets 
                WHERE status = 'open' 
                AND last_activity < ? 
                AND scheduled_close_at IS NULL
            `, [cutoffTime.toISOString()]);

            for (const ticket of inactiveTickets) {
                // Schedule the ticket for closure in 1 hour (grace period)
                const scheduleTime = new Date(Date.now() + (60 * 60 * 1000));
                
                await database.scheduleTask(
                    'close_ticket',
                    ticket.channel_id,
                    scheduleTime,
                    { reason: 'Auto-close due to inactivity' }
                );

                // Update the ticket to mark it as scheduled for closure
                await database.run(
                    'UPDATE tickets SET scheduled_close_at = ? WHERE id = ?',
                    [scheduleTime.toISOString(), ticket.id]
                );

                // Send warning message to the channel
                const channel = this.client.channels.cache.get(ticket.channel_id);
                if (channel) {
                    const embed = EmbedHelper.warning(
                        'Ticket Scheduled for Auto-Close',
                        `This ticket will be automatically closed in 1 hour due to inactivity. Send a message to cancel the auto-close.`
                    );

                    try {
                        await channel.send({ embeds: [embed] });
                    } catch (error) {
                        console.error('Error sending auto-close warning:', error);
                    }
                }
            }
        } catch (error) {
            console.error('Error checking inactive tickets:', error);
        }
    }

    async scheduleTicketClose(channelId, delay, reason = 'Scheduled close') {
        const scheduledFor = new Date(Date.now() + delay);
        
        await database.scheduleTask(
            'close_ticket',
            channelId,
            scheduledFor,
            { reason }
        );
    }

    async scheduleReminder(channelId, delay, message) {
        const scheduledFor = new Date(Date.now() + delay);
        
        await database.scheduleTask(
            'reminder',
            '0', // Not used for reminders
            scheduledFor,
            { channel_id: channelId, message }
        );
    }

    async cancelScheduledTasks(targetId, taskType = null) {
        try {
            let sql = 'UPDATE scheduled_tasks SET completed = TRUE WHERE target_id = ? AND completed = FALSE';
            const params = [targetId];

            if (taskType) {
                sql += ' AND task_type = ?';
                params.push(taskType);
            }

            await database.run(sql, params);
        } catch (error) {
            console.error('Error canceling scheduled tasks:', error);
        }
    }
}

module.exports = TaskScheduler;
